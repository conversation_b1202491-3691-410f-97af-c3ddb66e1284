# How Stock Search Works in the Scanner

## 🌍 Universe Building (11,464 stocks)

The system builds a comprehensive stock universe from multiple sources:

### 1. **Data Sources**
```python
# From universe_builder.py
def build() -> list[str]:
    tickers = sorted(set(_us() + _euronext()))
```

**US Markets (NYSE + NASDAQ)**:
- Downloads from: `https://ftp.nasdaqtrader.com/dynamic/SymDir/`
- Files: `nasdaqlisted.txt` + `otherlisted.txt`
- Includes all listed US stocks (~8,000-9,000 tickers)

**European Markets (Euronext)**:
- Downloads from: `https://stooq.com/db/h/`
- Countries: Netherlands (.AS), France (.PA), Belgium (.BR)
- Files: `euronext_nl.txt`, `euronext_fr.txt`, `euronext_be.txt`
- Includes major European stocks (~2,000-3,000 tickers)

### 2. **Caching System**
- **Cache File**: `~/.universe_cache.csv`
- **Duration**: 1 day (refreshes daily)
- **Fallback**: EU-only if US feeds fail
- **Never Crashes**: Always returns non-empty list

## 🔍 Stock Classification Process

### Classification Categories
```python
# From scoring.py
def classify(info: Dict[str, any]) -> str:
    cap  = info.get("marketCap", 0) or 0
    beta = info.get("beta", 1) or 1
    sector = (info.get("sector") or "").lower()

    if cap > 10e9 and beta < .8:
        return "stable"      # Large-cap, low volatility
    if sector and ("biotech" in sector or "pharma" in sector):
        return "pharma"      # Healthcare/biotech
    if sector and _HYPE & set(sector.split()):
        return "emerging"    # AI, quantum, EV, etc.
    return "moon"           # Everything else (high-risk)
```

### Category Definitions
1. **Stable**: Market cap > $10B + Beta < 0.8 (blue chips)
2. **Pharma**: Biotech or pharmaceutical sector
3. **Emerging**: Hype sectors (AI, quantum, hydrogen, fusion, blockchain, metaverse, EV, genomics, semiconductor)
4. **Moon**: All other stocks (high-risk, speculative)

## 🎯 Scanning Process

### Step 1: Universe Filtering
```python
# For each category, scan through universe
for t in universe[:200]:  # Limit to first 200 for speed
    try:
        ticker_info = yf.Ticker(t).info
        if classify(ticker_info) == cat:
            target.append(t)
            if len(target) >= 20:  # Max 20 per category
                break
    except Exception:
        continue  # Skip problematic tickers
```

### Step 2: Data Fetching & Scoring
```python
# For each target stock:
async def _process(t: str, cat: str):
    data = await fetch_all(t)  # Price, news, sentiment
    close, info = data["close"], data["info"]
    
    # Calculate performance score
    m = {"stable": 12, "emerging": 6, "pharma": 6, "moon": 3}[cat]
    price_chg = roi_change(close, m * 21)  # ROI over timeframe
    score = stock_score(price_chg, sentiment)
    
    return ticker, record, data
```

### Step 3: LLM Analysis
```python
# Top performers get LLM analysis
top = sorted(batch, key=lambda r: r[1]["score"], reverse=True)[:10]
prompts = [
    f"You are a CFA. Analyse {ticker} with this JSON {data} "
    "Return JSON {potential_gain_percent, opinion (BUY/SELL/HOLD), rationale}."
]
llm_out = await ask_gemini_batch(tickers, prompts, gemini_ask)
```

## ⚠️ Why You See HTTP Errors

### Normal Behavior
The HTTP errors (400, 404, 500) you're seeing are **completely normal** because:

1. **Large Universe**: 11,464 tickers include many that are:
   - Delisted companies
   - Invalid/outdated symbols
   - Suspended trading
   - Regional variations

2. **yfinance Limitations**: 
   - Yahoo Finance doesn't have data for all symbols
   - Some tickers return errors even if valid
   - Rate limiting can cause temporary failures

3. **Robust Error Handling**:
   ```python
   try:
       ticker_info = yf.Ticker(t).info
       if classify(ticker_info) == cat:
           target.append(t)
   except Exception:
       continue  # Skip and move to next ticker
   ```

### Success Metrics
Despite errors, the system successfully:
- ✅ Found 11 stable stocks (from ~200 attempts)
- ✅ Processes valid tickers and skips invalid ones
- ✅ Continues scanning until it finds enough stocks per category
- ✅ Never crashes due to individual ticker failures

## 📊 Performance Optimization

### Limits for Speed
- **Universe Scan**: First 200 tickers per category (not all 11,464)
- **Category Limit**: Max 20 stocks per category
- **LLM Analysis**: Top 10 performers only
- **Timeout**: 5-second timeout per ticker request

### Scheduling Strategy
Different categories have different scan frequencies:
- **Stable**: Weekly (Fridays) - blue chips change slowly
- **Emerging**: Daily - tech stocks more volatile  
- **Pharma**: Daily - news-driven sector
- **Moon**: Every 4 hours - highest volatility

## 🎯 Summary

The system intelligently:
1. **Builds** a comprehensive 11,464-stock universe from US + EU markets
2. **Classifies** stocks into 4 risk categories based on fundamentals
3. **Scans** efficiently by limiting search scope and handling errors gracefully
4. **Analyzes** top performers with LLM for investment insights
5. **Stores** results in database for tracking and dashboard display

The HTTP errors are expected noise from invalid tickers - the system is designed to handle this robustly and still find quality investment opportunities!
