"""
SQLite helper – creates ~/market.db on first run.
"""

from __future__ import annotations
import sqlite3, json, time
from contextlib import closing
from pathlib import Path
from typing import Any, Dict

DB_PATH = Path.home() / "market.db"

_SCHEMA = """
PRAGMA journal_mode=WAL;
CREATE TABLE IF NOT EXISTS stocks (
  ticker         TEXT PRIMARY KEY,
  category       TEXT,
  risk           TEXT,
  score          REAL,
  last_checked   INTEGER,
  price_now      REAL,
  roi_window     TEXT,
  potential_gain REAL,
  llm_opinion    TEXT
);
CREATE TABLE IF NOT EXISTS plans (
  ticker      TEXT,
  plan_json   TEXT,
  created_at  INTEGER
);
CREATE TABLE IF NOT EXISTS news (
  id           INTEGER PRIMARY KEY AUTOINCREMENT,
  ticker       TEXT,
  headline     TEXT,
  sentiment    REAL,
  source_url   TEXT,
  published_at INTEGER
);
CREATE INDEX IF NOT EXISTS news_ticker_time ON news(ticker, published_at DESC);
"""

_conn: sqlite3.Connection | None = None


def _conn_once() -> sqlite3.Connection:
    """
    Return a singleton SQLite connection and *always* ensure the schema exists.
    Even if market.db was created earlier but is empty (e.g. zero-byte file),
    running this function creates the required tables idempotently.
    """
    global _conn
    if _conn is None:
        _conn = sqlite3.connect(DB_PATH)
    # Ensure all tables exist every time; CREATE TABLE IF NOT EXISTS is safe.
    with _conn:
        _conn.executescript(_SCHEMA)
    return _conn


def upsert_stock(rec: Dict[str, Any]) -> None:
    cols = ", ".join(rec)
    vals = ":" + ", :".join(rec)
    sql = (
        f"INSERT INTO stocks ({cols}) VALUES ({vals}) "
        f"ON CONFLICT(ticker) DO UPDATE SET "
        + ", ".join(f"{c}=excluded.{c}" for c in rec if c != "ticker")
    )
    with _conn_once():
        _conn_once().execute(sql, rec)


def add_plan(ticker: str, plan_json: Dict[str, Any]) -> None:
    with _conn_once():
        _conn_once().execute(
            "INSERT INTO plans VALUES (?,?,?)",
            (ticker, json.dumps(plan_json), int(time.time())),
        )