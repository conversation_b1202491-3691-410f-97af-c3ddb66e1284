"""
APS → asyncio.Queue bridge.  Requires ticker_universe.csv.
"""

from __future__ import annotations
import asyncio, csv, pathlib
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger
import pathlib, csv, asyncio
from universe_builder import build as build_universe  # NEW
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger

ROOT = pathlib.Path(__file__).parent
UNIVERSE = ROOT / "ticker_universe.csv"

RULES = {
    "stable"  : "0 21 * * fri",     # Fri 21:00 UTC
    "emerging": "0 21 * * 1-5",     # Mon-Fri 21:00
    "pharma"  : "5 21 * * 1-5",     # Mon-Fri 21:05
    "moon"    : "0 */4 * * 1-5",    # Mon-Fri every 4 h
}


def _tickers() -> list[str]:
    """
    If ticker_universe.csv exists *and* isn’t empty we respect it;
    otherwise we auto-discover the full universe.
    """
    local = pathlib.Path(__file__).parent / "ticker_universe.csv"
    if local.exists() and local.stat().st_size:
        with local.open() as fh:
            return [r[0].strip().upper() for r in csv.reader(fh) if r and r[0].strip()]
    return build_universe()


def schedule_all(queue: asyncio.Queue) -> AsyncIOScheduler:
    universe = _tickers()
    sched = AsyncIOScheduler(timezone="UTC")
    for cat, expr in RULES.items():
        trig = CronTrigger.from_crontab(expr)
        sched.add_job(lambda c=cat: queue.put_nowait((c, universe)), trigger=trig)
    sched.add_job(build_universe, trigger="cron", hour=5, minute=0)
    sched.start()
    return sched