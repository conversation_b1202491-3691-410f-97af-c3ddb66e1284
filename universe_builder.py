"""
Download fresh symbol lists for NYSE, NASDAQ and Euronext.

Keeps a one-day cache at ~/.universe_cache.csv so we don’t hammer the feeds.
"""


from __future__ import annotations
import datetime, pathlib, os, requests, pandas as pd
import io
import logging                      # NEW
from requests.exceptions import RequestException, Timeout   # NEW
import csv
from urllib.parse import quote

CACHE = pathlib.Path.home() / ".universe_cache.csv"

# Pre-filters (can be overridden via env vars)
MIN_MCAP = float(os.getenv("MIN_MCAP", 100_000_000))       # €/$
MIN_DVOL = float(os.getenv("MIN_DVOL", 1_000_000))         # €/$

def _euronext() -> list[str]:
    """
    Fetch Euronext tickers from Stooq flat files.

    Stooq hosts daily dumps split by country:
      • https://stooq.com/db/h/euronext_nl.txt  (Netherlands)
      • https://stooq.com/db/h/euronext_fr.txt  (France)
      • https://stooq.com/db/h/euronext_be.txt  (Belgium)

    Each line is “SYMBOL,<rest>”.  Symbols already include the country suffix
    such as “ASML.AS”, “OR.PA”, “ABI.BR”.  We concatenate the three files and
    return the union.  On any network error we fall back to 10 large‑caps so
    the scanner never crashes.
    """
    base = "https://stooq.com/db/h"
    ccodes = ["nl", "fr", "be"]
    tickers: list[str] = []

    for cc in ccodes:
        url = f"{base}/euronext_{cc}.txt"
        try:
            txt = requests.get(url, timeout=10).text.splitlines()
            tickers.extend(
                row.split(",")[0].upper()
                for row in txt
                if row and not row.startswith("#")
            )
        except Exception as err:
            logging.warning("Stooq %s feed failed (%s)", cc, err)

    return tickers or [
        "ASML.AS", "AD.AS", "AIR.PA", "OR.PA", "MC.PA",
        "SAN.PA", "BNP.PA", "SU.PA", "ENGI.PA", "AI.PA",
    ]

# ────────────────────────────────────────────────────────────────────────────
# Hardened Nasdaq / NYSE fetch
# ────────────────────────────────────────────────────────────────────────────
def _nasdaq_file(fname: str) -> list[str]:
    """
    Download a symbol file from Nasdaq Trader.
    Tries three mirrors, 5-second timeout each.
    Returns [] if every mirror fails (never raises).
    """
    mirrors = [
        f"https://ftp.nasdaqtrader.com/dynamic/SymDir/{fname}",
        f"https://www.nasdaqtrader.com/dynamic/SymDir/{fname}",
        f"http://ftp.nasdaqtrader.com/dynamic/SymDir/{fname}",
    ]
    for url in mirrors:
        try:
            resp = requests.get(url, timeout=5)
            resp.raise_for_status()
            lines = resp.text.splitlines()
            return [
                line.split("|")[0]
                for line in lines[1:-1]          # skip header + checksum
                if "|" in line
            ]
        except (RequestException, Timeout) as err:
            logging.warning("Nasdaq mirror failed: %s – %s", url, err)
            continue
    logging.error("All Nasdaq mirrors timed-out; continuing without US symbols.")
    return []

def _us() -> list[str]:
    """Return NYSE + NASDAQ tickers, but never raise."""
    return _nasdaq_file("nasdaqlisted.txt") + _nasdaq_file("otherlisted.txt")

def build() -> list[str]:
    """
    Return the combined NYSE / NASDAQ / Euronext universe.

    * Uses ~/.universe_cache.csv when it is from today.
    * Falls back to EU-only symbols if US feeds time‑out.
    * Never raises – always returns a non‑empty list.
    """
    today_ord = datetime.date.today().toordinal()

    # Short‑circuit if today's cache exists
    if CACHE.exists() and CACHE.stat().st_mtime_ns // 86_400_000_000_000 == today_ord:
        try:
            cached = CACHE.read_text().splitlines()
            if cached:
                logging.info("Universe loaded from cache (%d symbols).", len(cached))
                return cached
        except Exception as err:
            logging.warning("Cache read failed, rebuilding: %s", err)

    # (re)build
    try:
        tickers = sorted(set(_us() + _euronext()))
    except Exception as e:
        logging.error("Universe build hit fatal error: %s – using EU fallback only", e)
        tickers = _euronext()

    # Write cache
    try:
        CACHE.write_text("\n".join(tickers))
    except Exception as err:
        logging.warning("Could not write universe cache: %s", err)

    logging.info("Universe size after build: %d", len(tickers))
    return tickers