"""
Rate limiting configuration for LLM calls.
Set environment variables to override defaults.
"""

import os
from typing import Dict, Any

# Default rate limits - OPTIMIZED FOR GEMINI FREE TIER
DEFAULT_CONFIG = {
    "LLM_DAILY_LIMIT": 1500,           # Gemini free tier daily limit
    "LLM_RATE_LIMIT_PER_MINUTE": 15,   # Gemini free tier: 15 requests per minute
    "LLM_BATCH_SIZE": 5,               # Smaller batches for free tier
    "LLM_DELAY_BETWEEN_CALLS": 4.5,    # 4.5s between calls = ~13/minute (safe margin)
    "LLM_MAX_CONCURRENT": 1,           # Sequential processing for free tier
    "LLM_MAX_STOCKS_PER_CATEGORY": 10, # Reduced default for free tier
}

def get_rate_limit_config() -> Dict[str, Any]:
    """Get current rate limiting configuration."""
    config = {}
    for key, default_value in DEFAULT_CONFIG.items():
        env_value = os.getenv(key)
        if env_value is not None:
            try:
                if isinstance(default_value, int):
                    config[key] = int(env_value)
                elif isinstance(default_value, float):
                    config[key] = float(env_value)
                else:
                    config[key] = env_value
            except ValueError:
                print(f"Warning: Invalid value for {key}: {env_value}, using default: {default_value}")
                config[key] = default_value
        else:
            config[key] = default_value
    
    return config

def print_config() -> None:
    """Print current rate limiting configuration."""
    config = get_rate_limit_config()
    
    print("⚙️  Rate Limiting Configuration:")
    print("=" * 40)
    print(f"Daily limit:           {config['LLM_DAILY_LIMIT']:,} calls")
    print(f"Per-minute limit:      {config['LLM_RATE_LIMIT_PER_MINUTE']} calls")
    print(f"Batch size:            {config['LLM_BATCH_SIZE']} stocks")
    print(f"Delay between calls:   {config['LLM_DELAY_BETWEEN_CALLS']}s")
    print(f"Max concurrent:        {config['LLM_MAX_CONCURRENT']}")
    print(f"Stocks per category:   {config['LLM_MAX_STOCKS_PER_CATEGORY']}")
    print()
    print("💡 To customize, set environment variables:")
    print("   export LLM_DAILY_LIMIT=500")
    print("   export LLM_MAX_STOCKS_PER_CATEGORY=10")
    print("   export LLM_MAX_CONCURRENT=2")

def estimate_calls_needed(
    categories: int = 4, 
    stocks_per_category: int = None
) -> Dict[str, int]:
    """Estimate LLM calls needed for a scan."""
    config = get_rate_limit_config()
    
    if stocks_per_category is None:
        stocks_per_category = config['LLM_MAX_STOCKS_PER_CATEGORY']
    
    total_calls = categories * stocks_per_category
    
    # Estimate time needed based on rate limits
    batch_size = config['LLM_BATCH_SIZE']
    delay_per_call = config['LLM_DELAY_BETWEEN_CALLS']
    max_concurrent = config['LLM_MAX_CONCURRENT']
    
    # Time per batch (assuming max concurrency)
    time_per_batch = (batch_size / max_concurrent) * delay_per_call
    num_batches = (total_calls + batch_size - 1) // batch_size  # Ceiling division
    estimated_time_minutes = (num_batches * time_per_batch) / 60
    
    return {
        "total_calls": total_calls,
        "num_batches": num_batches,
        "estimated_time_minutes": round(estimated_time_minutes, 1),
        "calls_per_category": stocks_per_category
    }

def check_quota_feasibility(
    categories: int = 4,
    stocks_per_category: int = None
) -> Dict[str, Any]:
    """Check if a scan is feasible given current quota."""
    from llm import get_rate_limit_status
    
    estimate = estimate_calls_needed(categories, stocks_per_category)
    status = get_rate_limit_status()
    
    feasible = estimate["total_calls"] <= status["daily_remaining"]
    
    return {
        "feasible": feasible,
        "calls_needed": estimate["total_calls"],
        "calls_available": status["daily_remaining"],
        "estimated_time": estimate["estimated_time_minutes"],
        "recommendation": get_recommendation(estimate, status)
    }

def get_recommendation(estimate: Dict[str, int], status: Dict[str, int]) -> str:
    """Get recommendation for scan configuration."""
    calls_needed = estimate["total_calls"]
    calls_available = status["daily_remaining"]
    
    if calls_needed <= calls_available:
        return "✅ Scan is feasible with current configuration"
    
    # Calculate how many stocks per category would fit
    max_stocks_per_category = calls_available // 4  # Assuming 4 categories
    
    if max_stocks_per_category > 0:
        return f"⚠️  Reduce to {max_stocks_per_category} stocks per category to fit quota"
    else:
        return f"❌ Not enough quota remaining ({calls_available} calls left)"

def suggest_optimal_config() -> Dict[str, int]:
    """Suggest optimal configuration based on remaining quota."""
    from llm import get_rate_limit_status
    
    status = get_rate_limit_status()
    remaining = status["daily_remaining"]
    
    if remaining >= 60:  # Full scan possible
        return {
            "categories": 4,
            "stocks_per_category": min(15, remaining // 4),
            "max_concurrent": 3
        }
    elif remaining >= 20:  # Reduced scan
        return {
            "categories": 2,  # Focus on stable and emerging
            "stocks_per_category": min(10, remaining // 2),
            "max_concurrent": 2
        }
    elif remaining >= 5:  # Minimal scan
        return {
            "categories": 1,  # Just stable
            "stocks_per_category": remaining,
            "max_concurrent": 1
        }
    else:
        return {
            "categories": 0,
            "stocks_per_category": 0,
            "max_concurrent": 1
        }

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "config":
            print_config()
        elif sys.argv[1] == "status":
            try:
                from llm import print_rate_limit_status
                print_rate_limit_status()
            except ImportError:
                print("❌ LLM module not available")
        elif sys.argv[1] == "estimate":
            categories = int(sys.argv[2]) if len(sys.argv) > 2 else 4
            stocks = int(sys.argv[3]) if len(sys.argv) > 3 else None
            
            estimate = estimate_calls_needed(categories, stocks)
            print(f"📊 Scan Estimate:")
            print(f"   Categories: {categories}")
            print(f"   Stocks per category: {estimate['calls_per_category']}")
            print(f"   Total LLM calls: {estimate['total_calls']}")
            print(f"   Estimated time: {estimate['estimated_time_minutes']} minutes")
            
            try:
                feasibility = check_quota_feasibility(categories, stocks)
                print(f"   {feasibility['recommendation']}")
            except ImportError:
                print("   (Cannot check quota - LLM module not available)")
        
        elif sys.argv[1] == "suggest":
            try:
                suggestion = suggest_optimal_config()
                print("💡 Suggested Configuration:")
                print(f"   Categories: {suggestion['categories']}")
                print(f"   Stocks per category: {suggestion['stocks_per_category']}")
                print(f"   Max concurrent: {suggestion['max_concurrent']}")
                
                if suggestion['categories'] > 0:
                    print(f"\n🚀 Run with:")
                    print(f"   python stock_agent_v2.py enhanced-scan \\")
                    print(f"     --max_stocks {suggestion['stocks_per_category']} \\")
                    print(f"     --max_concurrent {suggestion['max_concurrent']}")
                else:
                    print("\n❌ No quota remaining for analysis today")
            except ImportError:
                print("❌ Cannot suggest - LLM module not available")
    else:
        print("Usage:")
        print("  python rate_limit_config.py config    - Show configuration")
        print("  python rate_limit_config.py status    - Show current quota status")
        print("  python rate_limit_config.py estimate [categories] [stocks_per_cat] - Estimate calls needed")
        print("  python rate_limit_config.py suggest   - Suggest optimal configuration")
