"""
Gemini batcher with configurable daily quota and rate limiting.
"""

from __future__ import annotations
import asyncio, datetime, os, time
from typing import List, Tuple, Callable, Optional
import logging

# Configurable limits - UPDATED FOR GEMINI FREE TIER
LLM_DAILY_LIMIT = int(os.getenv("LLM_DAILY_LIMIT", "1500"))  # Gemini free tier daily limit
LLM_RATE_LIMIT_PER_MINUTE = int(os.getenv("LLM_RATE_LIMIT_PER_MINUTE", "15"))  # Gemini free tier: 15/minute
LLM_BATCH_SIZE = int(os.getenv("LLM_BATCH_SIZE", "5"))  # Smaller batches for free tier
LLM_DELAY_BETWEEN_CALLS = float(os.getenv("LLM_DELAY_BETWEEN_CALLS", "4.5"))  # 4.5s = ~13 calls/minute (safe margin)

# Global state
_calls_today, _last_date = 0, datetime.date.today()
_calls_this_minute, _last_minute = 0, datetime.datetime.now().replace(second=0, microsecond=0)
_lock = asyncio.Lock()

# Logging
logger = logging.getLogger(__name__)


def get_rate_limit_status() -> dict:
    """Get current rate limit status."""
    global _calls_today, _last_date, _calls_this_minute, _last_minute

    now = datetime.datetime.now()
    today = now.date()
    current_minute = now.replace(second=0, microsecond=0)

    # Reset daily counter
    if today != _last_date:
        _calls_today, _last_date = 0, today

    # Reset minute counter
    if current_minute != _last_minute:
        _calls_this_minute, _last_minute = 0, current_minute

    return {
        "calls_today": _calls_today,
        "daily_limit": LLM_DAILY_LIMIT,
        "daily_remaining": max(0, LLM_DAILY_LIMIT - _calls_today),
        "calls_this_minute": _calls_this_minute,
        "minute_limit": LLM_RATE_LIMIT_PER_MINUTE,
        "minute_remaining": max(0, LLM_RATE_LIMIT_PER_MINUTE - _calls_this_minute)
    }


def _can_make_calls(n: int) -> tuple[int, str]:
    """Check if we can make n calls and return (allowed_calls, reason)."""
    global _calls_today, _last_date, _calls_this_minute, _last_minute

    now = datetime.datetime.now()
    today = now.date()
    current_minute = now.replace(second=0, microsecond=0)

    # Reset counters if needed
    if today != _last_date:
        _calls_today, _last_date = 0, today
    if current_minute != _last_minute:
        _calls_this_minute, _last_minute = 0, current_minute

    # Check daily limit
    daily_remaining = max(0, LLM_DAILY_LIMIT - _calls_today)
    if daily_remaining == 0:
        return 0, f"Daily limit reached ({_calls_today}/{LLM_DAILY_LIMIT})"

    # Check minute limit
    minute_remaining = max(0, LLM_RATE_LIMIT_PER_MINUTE - _calls_this_minute)
    if minute_remaining == 0:
        return 0, f"Minute limit reached ({_calls_this_minute}/{LLM_RATE_LIMIT_PER_MINUTE})"

    # Return the minimum of what we want and what we can do
    allowed = min(n, daily_remaining, minute_remaining)
    reason = "OK" if allowed == n else f"Limited by quota (daily: {daily_remaining}, minute: {minute_remaining})"

    return allowed, reason


def _record_calls(n: int) -> None:
    """Record that n calls were made."""
    global _calls_today, _calls_this_minute
    _calls_today += n
    _calls_this_minute += n


async def ask_gemini_batch(
    tickers: List[str], prompts: List[str], gemini_func: Callable[[str], str]
) -> List[Tuple[str, str]]:
    """Legacy batch function - uses simple daily limit check."""
    async with _lock:
        allowed, reason = _can_make_calls(len(prompts))
        if allowed == 0:
            logger.warning(f"No LLM calls allowed: {reason}")
            return []

        if allowed < len(prompts):
            logger.warning(f"Limiting batch from {len(prompts)} to {allowed} calls: {reason}")
            tickers, prompts = tickers[:allowed], prompts[:allowed]

        _record_calls(allowed)

    async def _one(t, p):
        loop = asyncio.get_running_loop()
        reply = await loop.run_in_executor(None, gemini_func, p)
        return t, reply

    return await asyncio.gather(*[_one(t, p) for t, p in zip(tickers, prompts)])


async def ask_gemini_enhanced_batch(
    tickers: List[str],
    prompts: List[str],
    gemini_func: Callable[[str], str],
    max_concurrent: int = 3,
    show_progress: bool = True
) -> List[Tuple[str, str]]:
    """Enhanced batch function with configurable rate limiting and progress tracking."""

    if not tickers or not prompts:
        return []

    if len(tickers) != len(prompts):
        raise ValueError("Tickers and prompts lists must have the same length")

    # Check initial quota
    status = get_rate_limit_status()
    if status["daily_remaining"] == 0:
        logger.error(f"Daily LLM limit reached ({status['calls_today']}/{status['daily_limit']})")
        return []

    if show_progress:
        print(f"   📊 LLM Quota: {status['daily_remaining']}/{status['daily_limit']} calls remaining today")

    results = []
    total_requests = len(tickers)
    processed = 0

    # Process in batches to respect rate limits
    for i in range(0, total_requests, LLM_BATCH_SIZE):
        batch_tickers = tickers[i:i + LLM_BATCH_SIZE]
        batch_prompts = prompts[i:i + LLM_BATCH_SIZE]

        # Check if we can make this batch
        async with _lock:
            allowed, reason = _can_make_calls(len(batch_prompts))

            if allowed == 0:
                if "Daily limit" in reason:
                    logger.error(f"Daily LLM limit reached. Processed {processed}/{total_requests} stocks.")
                    break
                elif "Minute limit" in reason:
                    if show_progress:
                        print(f"   ⏱️  Rate limit reached, waiting 60 seconds...")
                    await asyncio.sleep(60)
                    # Try again after waiting
                    allowed, reason = _can_make_calls(len(batch_prompts))
                    if allowed == 0:
                        logger.error(f"Still rate limited after waiting: {reason}")
                        break

            if allowed < len(batch_prompts):
                logger.warning(f"Limiting batch from {len(batch_prompts)} to {allowed}: {reason}")
                batch_tickers = batch_tickers[:allowed]
                batch_prompts = batch_prompts[:allowed]

            _record_calls(len(batch_prompts))

        # Process this batch with concurrency control
        semaphore = asyncio.Semaphore(max_concurrent)

        async def _process_one(ticker, prompt):
            async with semaphore:
                max_retries = 3
                base_delay = 5  # Start with 5 second delay for 429 errors

                for attempt in range(max_retries):
                    try:
                        loop = asyncio.get_running_loop()
                        reply = await loop.run_in_executor(None, gemini_func, prompt)
                        # Add delay between calls to respect rate limits
                        await asyncio.sleep(LLM_DELAY_BETWEEN_CALLS)
                        return ticker, reply

                    except Exception as e:
                        error_str = str(e)

                        # Handle 429 quota errors specifically
                        if "429" in error_str or "quota" in error_str.lower():
                            if attempt < max_retries - 1:
                                retry_delay = base_delay * (2 ** attempt)  # Exponential backoff
                                logger.warning(f"Rate limit hit for {ticker}, retrying in {retry_delay}s (attempt {attempt + 1}/{max_retries})")
                                await asyncio.sleep(retry_delay)
                                continue
                            else:
                                logger.error(f"Rate limit exceeded for {ticker} after {max_retries} attempts")
                                return ticker, "[Gemini quota error]"
                        else:
                            # Other errors, don't retry
                            logger.error(f"LLM call failed for {ticker}: {e}")
                            return ticker, f"[LLM Error: {e}]"

                return ticker, "[Max retries exceeded]"

        # Execute batch
        batch_results = await asyncio.gather(*[
            _process_one(t, p) for t, p in zip(batch_tickers, batch_prompts)
        ])

        results.extend(batch_results)
        processed += len(batch_results)

        if show_progress:
            print(f"   ✅ Processed {processed}/{total_requests} stocks")

        # Brief pause between batches
        if i + LLM_BATCH_SIZE < total_requests:
            await asyncio.sleep(2)

    if show_progress:
        final_status = get_rate_limit_status()
        print(f"   📊 LLM calls used: {final_status['calls_today']}/{final_status['daily_limit']} today")

    return results


def print_rate_limit_status() -> None:
    """Print current rate limit status."""
    status = get_rate_limit_status()
    print(f"📊 LLM Rate Limit Status:")
    print(f"   Daily: {status['calls_today']}/{status['daily_limit']} ({status['daily_remaining']} remaining)")
    print(f"   This minute: {status['calls_this_minute']}/{status['minute_limit']} ({status['minute_remaining']} remaining)")
    print(f"   Batch size: {LLM_BATCH_SIZE}")
    print(f"   Delay between calls: {LLM_DELAY_BETWEEN_CALLS}s")