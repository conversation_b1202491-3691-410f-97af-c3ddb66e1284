"""
Async fetch of price + news + sentiment.
"""

from __future__ import annotations
import asyncio
from typing import Dict, Any
import yfinance as yf
import pandas as pd

try:
    from vaderSentiment.vaderSentiment import SentimentIntensityAnalyzer
    _VADER = SentimentIntensityAnalyzer()
except ImportError:
    _VADER = None


async def _news(ticker: str) -> Dict[str, Any]:
    loop = asyncio.get_running_loop()
    try:
        raw = await loop.run_in_executor(None, lambda: (yf.Ticker(ticker).news or [])[:5])
        # Handle missing 'title' key in news items
        heads = []
        for n in raw:
            if isinstance(n, dict):
                # Try different possible keys for the title
                title = n.get("title") or n.get("headline") or n.get("summary", "")
                if title:
                    heads.append(title)

        sent = 0.0
        if heads and _VADER:
            sent = sum(_VADER.polarity_scores(h)["compound"] for h in heads) / len(heads)
        return {"headlines": heads, "sentiment": sent}
    except Exception as e:
        # Return empty result on any error
        return {"headlines": [], "sentiment": 0.0}


async def fetch_all(ticker: str) -> Dict[str, Any]:
    loop = asyncio.get_running_loop()

    try:
        q: yf.Ticker = await loop.run_in_executor(None, yf.Ticker, ticker)
        info = q.info or {}

        # Get price history with error handling
        hist = await loop.run_in_executor(None, lambda: q.history(period="3y"))

        if hist.empty or "Close" not in hist.columns:
            # Return empty series if no price data
            close = pd.Series(dtype=float)
        else:
            close = hist["Close"]

        news = await _news(ticker)
        return {"info": info, "close": close, **news}

    except Exception as e:
        # Return minimal data structure on any error
        return {
            "info": {},
            "close": pd.Series(dtype=float),
            "headlines": [],
            "sentiment": 0.0
        }