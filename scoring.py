"""
Numeric category + score helpers.
"""

from __future__ import annotations
import pandas as pd
from typing import Dict, Set

_HYPE: Set[str] = {
    "ai", "quantum", "hydrogen", "fusion", "blockchain",
    "metaverse", "ev", "genomics", "semiconductor"
}

# Enhanced sector mappings based on actual Yahoo Finance data
_PHARMA_SECTORS = {"healthcare"}
_EMERGING_SECTORS = {"technology"}  # Technology sector often contains AI/semiconductor companies
_STABLE_SECTORS = {"consumer defensive", "utilities", "real estate"}


def classify(info: Dict[str, any]) -> str:
    if not info:  # Handle None or empty info
        return "moon"

    cap = info.get("marketCap", 0) or 0
    beta = info.get("beta", 1) or 1
    sector = (info.get("sector") or "").lower()
    industry = (info.get("industry") or "").lower()
    company_name = (info.get("longName") or info.get("shortName") or "").lower()

    # More detailed classification logic

    # 1. Stable mega-caps first (>$1T market cap) - these are stable regardless of sector
    if cap > 1000e9:  # $1 trillion+
        return "stable"

    # 2. Check for emerging companies
    emerging_keywords = [
        "semiconductor", "software", "artificial intelligence", "electric vehicle",
        "renewable", "solar", "genomics", "blockchain", "crypto", "quantum",
        "tesla", "nvidia", "palantir", "coinbase"  # Specific company indicators
    ]

    # Technology sector with specific emerging characteristics
    if sector in _EMERGING_SECTORS:
        # Large established tech companies (>$500B) are stable
        if cap > 500e9 and beta < 1.3:
            return "stable"
        # Others in tech sector are emerging
        else:
            return "emerging"

    # Non-tech sectors but with emerging keywords
    if (any(keyword in industry for keyword in emerging_keywords) or
        any(keyword in company_name for keyword in ["tesla", "electric", "crypto", "blockchain"])):
        return "emerging"

    # 3. Stable: Large cap (>$100B) with reasonable beta OR defensive sectors
    if (cap > 100e9 and beta < 1.5) or sector in _STABLE_SECTORS:
        return "stable"

    # 3. Pharma: Healthcare sector with specific characteristics
    if sector in _PHARMA_SECTORS:
        # Large healthcare companies are stable, smaller/biotech are pharma
        if cap > 100e9 and beta < 1.0:
            return "stable"  # Large pharma like JNJ, PFE
        else:
            return "pharma"  # Biotech and smaller pharma

    # Also check industry for pharma keywords
    if any(keyword in industry for keyword in ["biotech", "pharma", "drug", "medical device"]):
        return "pharma"

    # 4. Moon: Everything else (small cap, high beta, speculative)
    return "moon"


def roi_change(close: pd.Series, months: int) -> float:
    if close.empty or len(close) < months:
        return 0.0
    try:
        return (close.iloc[-1] / close.iloc[-months] - 1) * 100
    except (IndexError, ZeroDivisionError):
        return 0.0


def stock_score(price_change: float, sentiment: float) -> float:
    return price_change * 0.6 + sentiment * 40          # sentiment −1…+1