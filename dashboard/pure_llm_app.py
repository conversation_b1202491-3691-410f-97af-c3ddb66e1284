import streamlit as st

# MUST be first Streamlit command
st.set_page_config(layout="wide", page_title="Pure LLM Stock Analysis Dashboard")

import sys, pathlib
# Ensure project root is on PYTHONPATH
ROOT = pathlib.Path(__file__).resolve().parents[2]
if str(ROOT) not in sys.path:
    sys.path.insert(0, str(ROOT))

import pandas as pd
import numpy as np
from datetime import datetime
from pure_llm_db import (
    get_pure_llm_connection, get_top_performers, get_all_analyzed_stocks,
    get_analysis_stats, get_config
)

# Get database connection
@st.cache_resource
def get_database_connection():
    """Create database connection for Streamlit."""
    return get_pure_llm_connection()

DB = get_database_connection()

def format_score(score):
    """Format score for display."""
    if pd.isna(score) or score is None:
        return "N/A"
    return f"{score:.0f}"

def format_price(price):
    """Format price for display."""
    if pd.isna(price) or price is None:
        return "N/A"
    return f"${price:.2f}"

def format_timestamp(timestamp):
    """Format timestamp for display."""
    if pd.isna(timestamp) or timestamp is None or timestamp == 0:
        return "Never"
    return datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M')

def get_recommendation_emoji(recommendation):
    """Get emoji for recommendation."""
    if recommendation == "BUY":
        return "🟢"
    elif recommendation == "HOLD":
        return "🟡"
    elif recommendation == "SELL":
        return "🔴"
    else:
        return "⚪"

# Main dashboard
st.title("🧠 Pure LLM Stock Analysis Dashboard")
st.markdown("**Zero momentum bias - Pure fundamental analysis by AI**")

# Get configuration
top_stocks_per_category = int(get_config("top_stocks_per_category", "5"))

# Sidebar controls
st.sidebar.header("🔧 Dashboard Controls")
if st.sidebar.button("🔄 Refresh Data"):
    st.cache_resource.clear()
    st.rerun()

# Show analysis statistics
stats = get_analysis_stats()
if stats:
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        st.metric("📊 Total Stocks", f"{stats['total_stocks']:,}")
    with col2:
        st.metric("✅ Analyzed", f"{stats['analyzed_stocks']:,}")
    with col3:
        st.metric("🎯 Top Performers", f"{stats['top_performers']:,}")
    with col4:
        avg_score = stats['avg_overall_score'] if stats['avg_overall_score'] else 0
        st.metric("📈 Avg Score", f"{avg_score:.1f}")

# Recommendation summary
if stats and stats['analyzed_stocks'] > 0:
    st.markdown("### 📊 LLM Recommendations Summary")
    rec_col1, rec_col2, rec_col3 = st.columns(3)
    
    with rec_col1:
        buy_pct = (stats['buy_recommendations'] / stats['analyzed_stocks']) * 100
        st.metric("🟢 BUY", f"{stats['buy_recommendations']}", f"{buy_pct:.1f}%")
    
    with rec_col2:
        hold_pct = (stats['hold_recommendations'] / stats['analyzed_stocks']) * 100
        st.metric("🟡 HOLD", f"{stats['hold_recommendations']}", f"{hold_pct:.1f}%")
    
    with rec_col3:
        sell_pct = (stats['sell_recommendations'] / stats['analyzed_stocks']) * 100
        st.metric("🔴 SELL", f"{stats['sell_recommendations']}", f"{sell_pct:.1f}%")

st.markdown("---")

# Create tabs for dual views
tab1, tab2, tab3 = st.tabs([
    f"🎯 Top {top_stocks_per_category} Per Category", 
    "📊 All Analyzed Stocks", 
    "📈 Analysis Progress"
])

# Tab 1: Top Performers View
with tab1:
    st.header(f"🎯 Top {top_stocks_per_category} Performers by Category")
    st.markdown("*These are the highest-scoring stocks in each category based on pure LLM analysis*")
    
    categories = ["stable", "emerging", "pharma", "moon"]
    
    for category in categories:
        st.subheader(f"📊 {category.title()} Category")
        
        top_performers = get_top_performers(category, top_stocks_per_category)
        
        if top_performers:
            # Convert to DataFrame for display
            df = pd.DataFrame(top_performers)
            
            # Format columns for display
            display_df = pd.DataFrame({
                "Rank": range(1, len(df) + 1),
                "Ticker": df["ticker"],
                "Rec": [get_recommendation_emoji(rec) + " " + rec for rec in df["llm_recommendation"]],
                "Overall": [format_score(score) for score in df["llm_overall_score"]],
                "Growth": [format_score(score) for score in df["llm_growth_score"]],
                "Risk": [format_score(score) for score in df["llm_risk_score"]],
                "Sentiment": [format_score(score) for score in df["llm_sentiment_score"]],
                "Target": [format_price(price) for price in df["llm_target_price"]],
                "Current": [format_price(price) for price in df["current_price"]],
                "Last Analyzed": [format_timestamp(ts) for ts in df["last_analyzed"]],
                "LLM Advice": df["llm_advice"].str[:100] + "..."
            })
            
            st.dataframe(display_df, use_container_width=True, hide_index=True)
            
            # Show top pick details
            if len(top_performers) > 0:
                top_pick = top_performers[0]
                with st.expander(f"🎯 Top Pick Details: {top_pick['ticker']}"):
                    col1, col2, col3 = st.columns(3)
                    
                    with col1:
                        st.metric("Overall Score", format_score(top_pick['llm_overall_score']))
                        st.metric("Growth Score", format_score(top_pick['llm_growth_score']))
                    
                    with col2:
                        st.metric("Risk Score", format_score(top_pick['llm_risk_score']))
                        st.metric("Sentiment Score", format_score(top_pick['llm_sentiment_score']))
                    
                    with col3:
                        st.metric("Target Price", format_price(top_pick['llm_target_price']))
                        st.metric("Current Price", format_price(top_pick['current_price']))
                    
                    st.markdown("**🧠 LLM Investment Advice:**")
                    st.write(top_pick['llm_advice'])
        else:
            st.info(f"No analyzed stocks in {category} category yet.")
        
        st.markdown("---")

# Tab 2: All Analyzed Stocks
with tab2:
    st.header("📊 All Analyzed Stocks")
    st.markdown("*Complete database of LLM-analyzed stocks ranked by overall score*")
    
    # Filters
    col1, col2, col3 = st.columns(3)
    
    with col1:
        category_filter = st.selectbox(
            "Category Filter",
            ["All"] + categories,
            key="all_stocks_category"
        )
    
    with col2:
        recommendation_filter = st.selectbox(
            "Recommendation Filter",
            ["All", "BUY", "HOLD", "SELL"],
            key="all_stocks_recommendation"
        )
    
    with col3:
        min_score = st.slider(
            "Minimum Overall Score",
            0, 100, 0,
            key="all_stocks_min_score"
        )
    
    # Get all analyzed stocks
    all_stocks = get_all_analyzed_stocks(1000)
    
    if all_stocks:
        df = pd.DataFrame(all_stocks)
        
        # Apply filters
        if category_filter != "All":
            df = df[df["category"] == category_filter.lower()]
        
        if recommendation_filter != "All":
            df = df[df["llm_recommendation"] == recommendation_filter]
        
        df = df[df["llm_overall_score"] >= min_score]
        
        if not df.empty:
            # Format for display
            display_df = pd.DataFrame({
                "Rank": range(1, len(df) + 1),
                "Ticker": df["ticker"],
                "Category": df["category"].str.title(),
                "Rec": [get_recommendation_emoji(rec) + " " + rec for rec in df["llm_recommendation"]],
                "Overall": [format_score(score) for score in df["llm_overall_score"]],
                "Growth": [format_score(score) for score in df["llm_growth_score"]],
                "Risk": [format_score(score) for score in df["llm_risk_score"]],
                "Sentiment": [format_score(score) for score in df["llm_sentiment_score"]],
                "Target": [format_price(price) for price in df["llm_target_price"]],
                "Current": [format_price(price) for price in df["current_price"]],
                "Analyzed": df["analysis_count"],
                "Last Check": [format_timestamp(ts) for ts in df["last_analyzed"]],
                "LLM Advice": df["llm_advice"].str[:80] + "..."
            })
            
            st.dataframe(display_df, use_container_width=True, hide_index=True)
            
            # Summary statistics for filtered data
            st.markdown("### 📈 Filtered Results Summary")
            summary_col1, summary_col2, summary_col3, summary_col4 = st.columns(4)
            
            with summary_col1:
                st.metric("Filtered Stocks", len(df))
            with summary_col2:
                avg_overall = df["llm_overall_score"].mean()
                st.metric("Avg Overall Score", f"{avg_overall:.1f}")
            with summary_col3:
                buy_count = len(df[df["llm_recommendation"] == "BUY"])
                st.metric("BUY Recommendations", buy_count)
            with summary_col4:
                avg_target = df["llm_target_price"].mean()
                st.metric("Avg Target Price", format_price(avg_target))
        
        else:
            st.warning("No stocks match the selected filters.")
    
    else:
        st.info("No stocks have been analyzed yet. Run the analysis engine to populate data.")

# Tab 3: Analysis Progress
with tab3:
    st.header("📈 Analysis Progress")
    st.markdown("*Track the progress of LLM analysis across all categories*")
    
    # Get progress data
    conn = get_database_connection()
    
    progress_data = []
    for category in categories:
        cursor = conn.execute("""
            SELECT 
                COUNT(*) as total,
                COUNT(CASE WHEN llm_overall_score IS NOT NULL THEN 1 END) as analyzed,
                COUNT(CASE WHEN is_top_performer = 1 THEN 1 END) as top_performers,
                AVG(llm_overall_score) as avg_score
            FROM pure_llm_stocks 
            WHERE category = ?
        """, (category,))
        
        row = cursor.fetchone()
        progress_data.append({
            "Category": category.title(),
            "Total Stocks": row[0],
            "Analyzed": row[1],
            "Top Performers": row[2],
            "Progress %": (row[1] / row[0] * 100) if row[0] > 0 else 0,
            "Avg Score": row[3] if row[3] else 0
        })
    
    if progress_data:
        progress_df = pd.DataFrame(progress_data)
        
        # Display progress table
        st.dataframe(progress_df, use_container_width=True, hide_index=True)
        
        # Progress charts
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("#### 📊 Analysis Progress by Category")
            chart_data = progress_df[["Category", "Progress %"]].set_index("Category")
            st.bar_chart(chart_data)
        
        with col2:
            st.markdown("#### 📈 Average Scores by Category")
            score_data = progress_df[["Category", "Avg Score"]].set_index("Category")
            st.bar_chart(score_data)
    
    # Recent analysis activity
    st.markdown("### 🕐 Recent Analysis Activity")
    
    cursor = conn.execute("""
        SELECT ticker, category, llm_overall_score, llm_recommendation, last_analyzed
        FROM pure_llm_stocks 
        WHERE llm_overall_score IS NOT NULL
        ORDER BY last_analyzed DESC 
        LIMIT 20
    """)
    
    recent_activity = cursor.fetchall()
    
    if recent_activity:
        recent_df = pd.DataFrame(recent_activity, columns=[
            "Ticker", "Category", "Overall Score", "Recommendation", "Analyzed At"
        ])
        
        recent_df["Category"] = recent_df["Category"].str.title()
        recent_df["Overall Score"] = recent_df["Overall Score"].apply(format_score)
        recent_df["Recommendation"] = recent_df["Recommendation"].apply(
            lambda x: get_recommendation_emoji(x) + " " + x
        )
        recent_df["Analyzed At"] = recent_df["Analyzed At"].apply(format_timestamp)
        
        st.dataframe(recent_df, use_container_width=True, hide_index=True)
    else:
        st.info("No recent analysis activity.")

# Footer
st.markdown("---")
st.markdown("**🧠 Pure LLM Analysis System** - Zero momentum bias, pure fundamental analysis")
st.markdown(f"*Top stocks per category: {top_stocks_per_category} | Refresh schedule: Monday, Wednesday, Friday*")
