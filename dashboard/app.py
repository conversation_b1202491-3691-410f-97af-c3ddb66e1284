import streamlit as st

# MUST be first Streamlit command
st.set_page_config(layout="wide", page_title="Stock Analyst Pro Dashboard")

import sys, pathlib
# Ensure project root is on PYTHONPATH so "import db" works when Streamlit
# executes the script from inside the dashboard/ folder.
ROOT = pathlib.Path(__file__).resolve().parents[1]
if str(ROOT) not in sys.path:
    sys.path.insert(0, str(ROOT))

import pandas as pd, json, time
import numpy as np
import sqlite3
from pathlib import Path

# Create thread-safe database connection for Streamlit
DB_PATH = Path.home() / "market.db"

@st.cache_resource
def get_database_connection():
    """Create a thread-safe database connection for Streamlit."""
    conn = sqlite3.connect(DB_PATH, check_same_thread=False)
    # Ensure tables exist
    conn.executescript("""
        PRAGMA journal_mode=WAL;
        CREATE TABLE IF NOT EXISTS stocks (
          ticker         TEXT PRIMARY KEY,
          category       TEXT,
          risk           TEXT,
          score          REAL,
          last_checked   INTEGER,
          price_now      REAL,
          roi_window     TEXT,
          potential_gain REAL,
          llm_opinion    TEXT
        );
        CREATE TABLE IF NOT EXISTS plans (
          ticker      TEXT,
          plan_json   TEXT,
          created_at  INTEGER
        );
    """)
    return conn

DB = get_database_connection()


def clean_dataframe_for_display(df):
    """Clean dataframe to handle mixed data types that cause PyArrow issues."""
    if df.empty:
        return df

    # Create a copy to avoid modifying the original
    df_clean = df.copy()

    # Handle potential_gain column - convert to consistent string format
    if 'potential_gain' in df_clean.columns:
        def format_potential_gain(value):
            if pd.isna(value) or value is None:
                return "N/A"
            elif isinstance(value, (int, float)):
                if np.isnan(value):
                    return "N/A"
                return f"{value:.1f}%"
            else:
                # Already a string, clean it up
                str_val = str(value)
                if str_val.lower() in ['none', 'nan', 'null', '']:
                    return "N/A"
                elif not str_val.endswith('%'):
                    return f"{str_val}%"
                return str_val

        df_clean['potential_gain'] = df_clean['potential_gain'].apply(format_potential_gain)

    # Handle other numeric columns that might have mixed types
    numeric_columns = ['score', 'price_now', 'last_checked']
    for col in numeric_columns:
        if col in df_clean.columns:
            df_clean[col] = pd.to_numeric(df_clean[col], errors='coerce')

    # Convert timestamp to readable format
    if 'last_checked' in df_clean.columns:
        df_clean['last_checked'] = pd.to_datetime(df_clean['last_checked'], unit='s', errors='coerce')
        df_clean['last_checked'] = df_clean['last_checked'].dt.strftime('%Y-%m-%d %H:%M')

    return df_clean


st.title("🚀 Stock Analyst Pro - Enhanced LLM Edition")

tab1, tab2, tab3 = st.tabs(["📊 Top Performers", "🎯 Opportunities", "📈 Recent Analysis"])

with tab1:
    st.header("Top 100 Stocks by Score")
    df = pd.read_sql("SELECT * FROM stocks ORDER BY score DESC LIMIT 100", DB)
    df_clean = clean_dataframe_for_display(df)

    if not df_clean.empty:
        st.dataframe(df_clean, use_container_width=True)

        # Show summary stats
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("Total Stocks", len(df_clean))
        with col2:
            avg_score = df_clean['score'].mean()
            st.metric("Average Score", f"{avg_score:.2f}")
        with col3:
            buy_count = len(df_clean[df_clean['llm_opinion'].str.contains('BUY', na=False)])
            st.metric("LLM BUY Recommendations", buy_count)
        with col4:
            categories = df_clean['category'].value_counts()
            st.metric("Categories", len(categories))
    else:
        st.info("No stock data available. Run a scan to populate the database.")

with tab2:
    st.header("Investment Opportunities by Category")
    cat = st.selectbox("Category", ["emerging", "pharma", "moon", "stable"])

    # Use CASE to handle potential_gain ordering with mixed types
    df2 = pd.read_sql("""
        SELECT * FROM stocks
        WHERE category=?
        ORDER BY
            CASE
                WHEN potential_gain IS NULL THEN 0
                WHEN typeof(potential_gain) = 'real' THEN potential_gain
                ELSE 0
            END DESC,
            score DESC
        LIMIT 50
    """, DB, params=(cat,))

    df2_clean = clean_dataframe_for_display(df2)

    if not df2_clean.empty:
        st.dataframe(df2_clean, use_container_width=True)
    else:
        st.info(f"No {cat} stocks found. Run a scan to analyze stocks in this category.")
    if not df2_clean.empty:
        # Show top opportunity details
        top_stock = df2_clean.iloc[0]
        st.subheader(f"🎯 Top {cat.title()} Opportunity: {top_stock['ticker']}")

        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Score", f"{top_stock['score']:.2f}")
        with col2:
            st.metric("Potential Gain", top_stock['potential_gain'])
        with col3:
            st.metric("Risk Level", top_stock['risk'])

        if pd.notna(top_stock['llm_opinion']):
            st.write("**LLM Analysis:**", top_stock['llm_opinion'])

        # Show investment plan if available
        tkr = top_stock["ticker"]
        plan = DB.execute(
            "SELECT plan_json FROM plans WHERE ticker=? ORDER BY created_at DESC LIMIT 1",
            (tkr,),
        ).fetchone()

        if plan:
            st.subheader(f"📋 Latest Investment Plan for {tkr}")
            try:
                plan_data = json.loads(plan[0])
                st.json(plan_data)
            except json.JSONDecodeError:
                st.error("Invalid plan data format")
        else:
            st.info(f"No investment plan stored for {tkr} yet. Use the CLI to generate one.")

with tab3:
    st.header("Recent LLM Analysis")

    # Show recent stocks with LLM opinions
    recent_df = pd.read_sql("""
        SELECT ticker, category, score, potential_gain, llm_opinion, last_checked
        FROM stocks
        WHERE llm_opinion IS NOT NULL
        ORDER BY last_checked DESC
        LIMIT 20
    """, DB)

    recent_clean = clean_dataframe_for_display(recent_df)

    if not recent_clean.empty:
        st.dataframe(recent_clean, use_container_width=True)

        # Show LLM recommendation breakdown
        st.subheader("📊 LLM Recommendation Summary")

        buy_stocks = recent_clean[recent_clean['llm_opinion'].str.contains('BUY', na=False)]
        hold_stocks = recent_clean[recent_clean['llm_opinion'].str.contains('HOLD', na=False)]
        sell_stocks = recent_clean[recent_clean['llm_opinion'].str.contains('SELL', na=False)]

        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("🟢 BUY", len(buy_stocks))
            if not buy_stocks.empty:
                st.write("**Top BUY picks:**")
                for _, stock in buy_stocks.head(3).iterrows():
                    st.write(f"• {stock['ticker']} ({stock['potential_gain']})")

        with col2:
            st.metric("🟡 HOLD", len(hold_stocks))
            if not hold_stocks.empty:
                st.write("**HOLD positions:**")
                for _, stock in hold_stocks.head(3).iterrows():
                    st.write(f"• {stock['ticker']} ({stock['potential_gain']})")

        with col3:
            st.metric("🔴 SELL", len(sell_stocks))
            if not sell_stocks.empty:
                st.write("**SELL recommendations:**")
                for _, stock in sell_stocks.head(3).iterrows():
                    st.write(f"• {stock['ticker']} ({stock['potential_gain']})")
    else:
        st.info("No recent LLM analysis available. Run a scan to get AI-powered stock analysis.")

# Add refresh button and auto-refresh option
st.sidebar.header("🔄 Dashboard Controls")
if st.sidebar.button("Refresh Data"):
    st.rerun()

auto_refresh = st.sidebar.checkbox("Auto-refresh (30s)")
if auto_refresh:
    time.sleep(30)
    st.rerun()