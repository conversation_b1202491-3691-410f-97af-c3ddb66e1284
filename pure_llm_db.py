"""
Pure LLM database schema - Zero momentum bias.
Stores only LLM-driven fundamental analysis results.
"""

import sqlite3
import time
from pathlib import Path
from typing import Any, Dict, List, Optional
from datetime import datetime

DB_PATH = Path.home() / "pure_llm_stocks.db"

# Pure LLM schema - NO momentum calculations
_PURE_LLM_SCHEMA = """
PRAGMA journal_mode=WAL;

-- Pure LLM analysis table
CREATE TABLE IF NOT EXISTS pure_llm_stocks (
  ticker                TEXT PRIMARY KEY,
  category              TEXT,  -- stable/emerging/pharma/moon
  
  -- LLM Scores (0-100)
  llm_overall_score     REAL,  -- Primary ranking metric
  llm_growth_score      REAL,  -- Growth potential
  llm_risk_score        REAL,  -- Risk assessment (100=very risky)
  llm_sentiment_score   REAL,  -- Market sentiment
  
  -- LLM Analysis
  llm_recommendation    TEXT,  -- BUY/HOLD/SELL
  llm_advice           TEXT,  -- Investment thesis
  llm_target_price     REAL,  -- Target price
  llm_confidence       REAL,  -- LLM confidence (0-100)
  
  -- Stock Data
  current_price        REAL,
  market_cap           REAL,
  sector               TEXT,
  industry             TEXT,
  
  -- Tracking
  last_analyzed        INTEGER,  -- Timestamp
  analysis_count       INTEGER DEFAULT 1,  -- How many times analyzed
  is_top_performer     BOOLEAN DEFAULT 0,  -- Currently in top N
  
  -- Technical Indicators (for reference)
  rsi_14               REAL,
  macd_signal          TEXT,
  trend_direction      TEXT,
  support_level        REAL,
  resistance_level     REAL
);

-- Analysis history for tracking changes
CREATE TABLE IF NOT EXISTS analysis_history (
  id                   INTEGER PRIMARY KEY AUTOINCREMENT,
  ticker               TEXT,
  analysis_date        INTEGER,
  overall_score        REAL,
  growth_score         REAL,
  risk_score           REAL,
  sentiment_score      REAL,
  recommendation       TEXT,
  advice               TEXT,
  target_price         REAL,
  
  FOREIGN KEY (ticker) REFERENCES pure_llm_stocks (ticker)
);

-- Top performers tracking per category
CREATE TABLE IF NOT EXISTS top_performers (
  id                   INTEGER PRIMARY KEY AUTOINCREMENT,
  category             TEXT,
  ticker               TEXT,
  overall_score        REAL,
  rank_position        INTEGER,
  added_date           INTEGER,
  
  UNIQUE(category, ticker)
);

-- Analysis queue for smart rotation
CREATE TABLE IF NOT EXISTS analysis_queue (
  id                   INTEGER PRIMARY KEY AUTOINCREMENT,
  ticker               TEXT UNIQUE,
  category             TEXT,
  priority             INTEGER,  -- 1=unanalyzed, 2=analyzed, 3=top_performer
  last_queued          INTEGER
);

-- Configuration table
CREATE TABLE IF NOT EXISTS config (
  key                  TEXT PRIMARY KEY,
  value                TEXT,
  updated_at           INTEGER
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_overall_score ON pure_llm_stocks(llm_overall_score DESC);
CREATE INDEX IF NOT EXISTS idx_category_score ON pure_llm_stocks(category, llm_overall_score DESC);
CREATE INDEX IF NOT EXISTS idx_last_analyzed ON pure_llm_stocks(last_analyzed);
CREATE INDEX IF NOT EXISTS idx_top_performers ON pure_llm_stocks(is_top_performer, category);
CREATE INDEX IF NOT EXISTS idx_analysis_history_date ON analysis_history(analysis_date DESC);
"""

_conn: sqlite3.Connection | None = None


def get_pure_llm_connection() -> sqlite3.Connection:
    """Get pure LLM database connection."""
    global _conn
    if _conn is None:
        _conn = sqlite3.connect(DB_PATH, check_same_thread=False)
        _conn.row_factory = sqlite3.Row
    
    # Ensure schema exists
    with _conn:
        _conn.executescript(_PURE_LLM_SCHEMA)
    
    return _conn


def upsert_llm_analysis(analysis_data: Dict[str, Any]) -> None:
    """Insert or update LLM analysis."""
    conn = get_pure_llm_connection()
    
    # Prepare the record
    cols = ", ".join(analysis_data.keys())
    vals = ":" + ", :".join(analysis_data.keys())
    
    # Create upsert SQL
    sql = (
        f"INSERT INTO pure_llm_stocks ({cols}) VALUES ({vals}) "
        f"ON CONFLICT(ticker) DO UPDATE SET "
        + ", ".join(f"{c}=excluded.{c}" for c in analysis_data.keys() if c != "ticker")
    )
    
    with conn:
        conn.execute(sql, analysis_data)


def add_to_analysis_history(ticker: str, analysis_data: Dict[str, Any]) -> None:
    """Add analysis to history."""
    conn = get_pure_llm_connection()
    
    history_record = {
        "ticker": ticker,
        "analysis_date": int(time.time()),
        "overall_score": analysis_data.get("llm_overall_score"),
        "growth_score": analysis_data.get("llm_growth_score"),
        "risk_score": analysis_data.get("llm_risk_score"),
        "sentiment_score": analysis_data.get("llm_sentiment_score"),
        "recommendation": analysis_data.get("llm_recommendation"),
        "advice": analysis_data.get("llm_advice"),
        "target_price": analysis_data.get("llm_target_price")
    }
    
    with conn:
        conn.execute("""
            INSERT INTO analysis_history 
            (ticker, analysis_date, overall_score, growth_score, risk_score, 
             sentiment_score, recommendation, advice, target_price)
            VALUES (:ticker, :analysis_date, :overall_score, :growth_score, :risk_score,
                    :sentiment_score, :recommendation, :advice, :target_price)
        """, history_record)


def get_top_performers(category: str, limit: int = 5) -> List[Dict[str, Any]]:
    """Get top performers in a category."""
    conn = get_pure_llm_connection()
    
    cursor = conn.execute("""
        SELECT * FROM pure_llm_stocks 
        WHERE category = ? AND llm_overall_score IS NOT NULL
        ORDER BY llm_overall_score DESC 
        LIMIT ?
    """, (category, limit))
    
    return [dict(row) for row in cursor.fetchall()]


def get_all_analyzed_stocks(limit: int = 1000) -> List[Dict[str, Any]]:
    """Get all analyzed stocks."""
    conn = get_pure_llm_connection()
    
    cursor = conn.execute("""
        SELECT * FROM pure_llm_stocks 
        WHERE llm_overall_score IS NOT NULL
        ORDER BY llm_overall_score DESC 
        LIMIT ?
    """, (limit,))
    
    return [dict(row) for row in cursor.fetchall()]


def update_top_performers(category: str, limit: int = 5) -> None:
    """Update top performers tracking."""
    conn = get_pure_llm_connection()
    
    with conn:
        # Clear existing top performer flags for this category
        conn.execute("""
            UPDATE pure_llm_stocks 
            SET is_top_performer = 0 
            WHERE category = ?
        """, (category,))
        
        # Get top performers
        top_performers = get_top_performers(category, limit)
        
        # Update top performer flags
        for performer in top_performers:
            conn.execute("""
                UPDATE pure_llm_stocks 
                SET is_top_performer = 1 
                WHERE ticker = ?
            """, (performer['ticker'],))
        
        # Update top_performers table
        conn.execute("DELETE FROM top_performers WHERE category = ?", (category,))
        
        for i, performer in enumerate(top_performers):
            conn.execute("""
                INSERT INTO top_performers 
                (category, ticker, overall_score, rank_position, added_date)
                VALUES (?, ?, ?, ?, ?)
            """, (category, performer['ticker'], performer['llm_overall_score'], 
                  i + 1, int(time.time())))


def get_next_stocks_to_analyze(category: str, limit: int = 10) -> List[str]:
    """Get next stocks to analyze with smart prioritization."""
    conn = get_pure_llm_connection()
    
    # Priority 1: Unanalyzed stocks
    cursor = conn.execute("""
        SELECT ticker FROM pure_llm_stocks 
        WHERE category = ? AND llm_overall_score IS NULL
        ORDER BY RANDOM()
        LIMIT ?
    """, (category, limit))
    
    unanalyzed = [row[0] for row in cursor.fetchall()]
    
    if len(unanalyzed) >= limit:
        return unanalyzed
    
    # Priority 2: Analyzed non-top performers (oldest first)
    remaining = limit - len(unanalyzed)
    cursor = conn.execute("""
        SELECT ticker FROM pure_llm_stocks 
        WHERE category = ? AND llm_overall_score IS NOT NULL AND is_top_performer = 0
        ORDER BY last_analyzed ASC
        LIMIT ?
    """, (category, remaining))
    
    analyzed = [row[0] for row in cursor.fetchall()]
    
    return unanalyzed + analyzed


def get_top_performers_for_refresh(category: str) -> List[str]:
    """Get top performers that need refresh (Mon/Wed/Fri)."""
    conn = get_pure_llm_connection()
    
    cursor = conn.execute("""
        SELECT ticker FROM pure_llm_stocks 
        WHERE category = ? AND is_top_performer = 1
        ORDER BY llm_overall_score DESC
    """, (category,))
    
    return [row[0] for row in cursor.fetchall()]


def get_analysis_stats() -> Dict[str, Any]:
    """Get analysis statistics."""
    conn = get_pure_llm_connection()
    
    cursor = conn.execute("""
        SELECT 
            COUNT(*) as total_stocks,
            COUNT(CASE WHEN llm_overall_score IS NOT NULL THEN 1 END) as analyzed_stocks,
            COUNT(CASE WHEN is_top_performer = 1 THEN 1 END) as top_performers,
            AVG(llm_overall_score) as avg_overall_score,
            COUNT(CASE WHEN llm_recommendation = 'BUY' THEN 1 END) as buy_recommendations,
            COUNT(CASE WHEN llm_recommendation = 'HOLD' THEN 1 END) as hold_recommendations,
            COUNT(CASE WHEN llm_recommendation = 'SELL' THEN 1 END) as sell_recommendations
        FROM pure_llm_stocks
    """)
    
    return dict(cursor.fetchone())


def set_config(key: str, value: str) -> None:
    """Set configuration value."""
    conn = get_pure_llm_connection()
    
    with conn:
        conn.execute("""
            INSERT INTO config (key, value, updated_at) 
            VALUES (?, ?, ?)
            ON CONFLICT(key) DO UPDATE SET 
                value = excluded.value,
                updated_at = excluded.updated_at
        """, (key, value, int(time.time())))


def get_config(key: str, default: str = None) -> Optional[str]:
    """Get configuration value."""
    conn = get_pure_llm_connection()
    
    cursor = conn.execute("SELECT value FROM config WHERE key = ?", (key,))
    row = cursor.fetchone()
    return row[0] if row else default


if __name__ == "__main__":
    # Test database
    conn = get_pure_llm_connection()
    print("✅ Pure LLM database schema created successfully")
    
    # Show table info
    cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = [row[0] for row in cursor.fetchall()]
    print(f"📊 Created tables: {', '.join(tables)}")
    
    # Test configuration
    set_config("top_stocks_per_category", "5")
    print(f"📝 Config test: {get_config('top_stocks_per_category')}")
