"""
Smart rotation engine for pure LLM stock analysis.
Ensures all stocks get analyzed with intelligent prioritization.
"""

import asyncio
import logging
import random
import time
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import yfinance as yf

from pure_llm_system import analyze_stock_with_llm, LLMAnalysis
from pure_llm_db import (
    get_pure_llm_connection, upsert_llm_analysis, add_to_analysis_history,
    get_top_performers, update_top_performers, get_next_stocks_to_analyze,
    get_top_performers_for_refresh, get_analysis_stats, set_config, get_config
)
from scoring import classify
from llm import ask_gemini_enhanced_batch

logger = logging.getLogger(__name__)


class SmartRotationEngine:
    """Manages intelligent stock analysis rotation."""
    
    def __init__(self, top_stocks_per_category: int = 5):
        self.top_stocks_per_category = top_stocks_per_category
        self.categories = ["stable", "emerging", "pharma", "moon"]
        
        # Save configuration
        set_config("top_stocks_per_category", str(top_stocks_per_category))
    
    async def initialize_universe(self, universe: List[str]) -> None:
        """Initialize the stock universe in database."""
        conn = get_pure_llm_connection()
        
        print(f"🌍 Initializing universe with {len(universe)} stocks...")
        
        # Categorize and insert stocks
        categorized = {"stable": [], "emerging": [], "pharma": [], "moon": []}
        
        for ticker in universe:
            try:
                info = yf.Ticker(ticker).info or {}
                category = classify(info)
                categorized[category].append(ticker)
                
                # Insert stock if not exists
                with conn:
                    conn.execute("""
                        INSERT OR IGNORE INTO pure_llm_stocks 
                        (ticker, category, current_price, market_cap, sector, industry, last_analyzed)
                        VALUES (?, ?, ?, ?, ?, ?, 0)
                    """, (
                        ticker, category, 
                        info.get("currentPrice", 0),
                        info.get("marketCap", 0),
                        info.get("sector", ""),
                        info.get("industry", "")
                    ))
                    
            except Exception as e:
                logger.debug(f"Failed to categorize {ticker}: {e}")
                continue
        
        for cat, tickers in categorized.items():
            print(f"  📊 {cat}: {len(tickers)} stocks")
        
        print("✅ Universe initialization complete")
    
    def is_refresh_day(self) -> bool:
        """Check if today is a refresh day (Mon/Wed/Fri)."""
        today = datetime.now().weekday()  # 0=Monday, 6=Sunday
        return today in [0, 2, 4]  # Monday, Wednesday, Friday
    
    async def run_analysis_cycle(self, max_calls_per_cycle: int = 20) -> Dict[str, Any]:
        """Run one analysis cycle with smart prioritization."""
        print(f"\n🔄 Starting analysis cycle (max {max_calls_per_cycle} calls)")
        
        results = {
            "analyzed": 0,
            "refreshed": 0,
            "new_top_performers": [],
            "categories_processed": []
        }
        
        calls_used = 0
        
        # Check if it's a refresh day
        if self.is_refresh_day():
            print("📅 Refresh day - prioritizing top performers")
            calls_used += await self._refresh_top_performers(max_calls_per_cycle // 2)
            results["refreshed"] = calls_used
        
        # Analyze new stocks
        remaining_calls = max_calls_per_cycle - calls_used
        if remaining_calls > 0:
            new_analyzed = await self._analyze_new_stocks(remaining_calls)
            results["analyzed"] = new_analyzed
            calls_used += new_analyzed
        
        # Update top performers lists
        for category in self.categories:
            old_top = set(t['ticker'] for t in get_top_performers(category, self.top_stocks_per_category))
            update_top_performers(category, self.top_stocks_per_category)
            new_top = set(t['ticker'] for t in get_top_performers(category, self.top_stocks_per_category))
            
            new_additions = new_top - old_top
            if new_additions:
                results["new_top_performers"].extend(list(new_additions))
                print(f"🎯 New top performers in {category}: {', '.join(new_additions)}")
        
        # Show statistics
        stats = get_analysis_stats()
        print(f"\n📊 Analysis Statistics:")
        print(f"  Total stocks: {stats['total_stocks']}")
        print(f"  Analyzed: {stats['analyzed_stocks']}")
        print(f"  Top performers: {stats['top_performers']}")
        print(f"  Average score: {stats['avg_overall_score']:.1f}")
        print(f"  Recommendations: {stats['buy_recommendations']} BUY, {stats['hold_recommendations']} HOLD, {stats['sell_recommendations']} SELL")
        
        return results
    
    async def _refresh_top_performers(self, max_calls: int) -> int:
        """Refresh analysis of top performers."""
        print("🔄 Refreshing top performers...")
        
        calls_used = 0
        all_top_performers = []
        
        # Collect all top performers
        for category in self.categories:
            top_performers = get_top_performers_for_refresh(category)
            all_top_performers.extend([(ticker, category) for ticker in top_performers])
        
        # Shuffle for fairness
        random.shuffle(all_top_performers)
        
        # Analyze up to max_calls
        to_analyze = all_top_performers[:max_calls]
        
        for ticker, category in to_analyze:
            try:
                analysis = await self._analyze_single_stock(ticker)
                if analysis:
                    await self._store_analysis(ticker, category, analysis)
                    calls_used += 1
                    print(f"  🔄 Refreshed {ticker}: {analysis.recommendation} ({analysis.overall_score:.0f})")
                
            except Exception as e:
                logger.error(f"Failed to refresh {ticker}: {e}")
        
        print(f"✅ Refreshed {calls_used} top performers")
        return calls_used
    
    async def _analyze_new_stocks(self, max_calls: int) -> int:
        """Analyze new stocks with smart prioritization."""
        print("🆕 Analyzing new stocks...")
        
        calls_used = 0
        calls_per_category = max(1, max_calls // len(self.categories))
        
        for category in self.categories:
            if calls_used >= max_calls:
                break
            
            # Get next stocks to analyze for this category
            stocks_to_analyze = get_next_stocks_to_analyze(category, calls_per_category)
            
            if not stocks_to_analyze:
                print(f"  📊 {category}: No stocks to analyze")
                continue
            
            print(f"  📊 {category}: Analyzing {len(stocks_to_analyze)} stocks")
            
            for ticker in stocks_to_analyze:
                if calls_used >= max_calls:
                    break
                
                try:
                    analysis = await self._analyze_single_stock(ticker)
                    if analysis:
                        await self._store_analysis(ticker, category, analysis)
                        calls_used += 1
                        print(f"    ✅ {ticker}: {analysis.recommendation} ({analysis.overall_score:.0f})")
                    
                except Exception as e:
                    logger.error(f"Failed to analyze {ticker}: {e}")
        
        print(f"✅ Analyzed {calls_used} new stocks")
        return calls_used
    
    async def _analyze_single_stock(self, ticker: str) -> Optional[LLMAnalysis]:
        """Analyze a single stock with LLM."""
        from stock_agent_v2 import gemini_ask
        
        # Add delay to respect rate limits
        await asyncio.sleep(4.5)  # 4.5s delay for free tier
        
        return await analyze_stock_with_llm(ticker, gemini_ask)
    
    async def _store_analysis(self, ticker: str, category: str, analysis: LLMAnalysis) -> None:
        """Store analysis results in database."""
        analysis_data = {
            "ticker": ticker,
            "category": category,
            "llm_overall_score": analysis.overall_score,
            "llm_growth_score": analysis.growth_score,
            "llm_risk_score": analysis.risk_score,
            "llm_sentiment_score": analysis.sentiment_score,
            "llm_recommendation": analysis.recommendation,
            "llm_advice": analysis.advice,
            "llm_target_price": analysis.target_price,
            "llm_confidence": analysis.confidence,
            "last_analyzed": int(time.time()),
            "analysis_count": 1  # Will be incremented by database
        }
        
        # Update analysis count
        conn = get_pure_llm_connection()
        with conn:
            # Get current count
            cursor = conn.execute("SELECT analysis_count FROM pure_llm_stocks WHERE ticker = ?", (ticker,))
            row = cursor.fetchone()
            if row:
                analysis_data["analysis_count"] = row[0] + 1
        
        # Store analysis
        upsert_llm_analysis(analysis_data)
        add_to_analysis_history(ticker, analysis_data)
    
    def get_top_performers_summary(self) -> Dict[str, List[Dict]]:
        """Get summary of top performers by category."""
        summary = {}
        for category in self.categories:
            top_performers = get_top_performers(category, self.top_stocks_per_category)
            summary[category] = top_performers
        return summary
    
    def get_analysis_progress(self) -> Dict[str, Any]:
        """Get analysis progress statistics."""
        conn = get_pure_llm_connection()
        
        progress = {}
        for category in self.categories:
            cursor = conn.execute("""
                SELECT 
                    COUNT(*) as total,
                    COUNT(CASE WHEN llm_overall_score IS NOT NULL THEN 1 END) as analyzed,
                    COUNT(CASE WHEN is_top_performer = 1 THEN 1 END) as top_performers
                FROM pure_llm_stocks 
                WHERE category = ?
            """, (category,))
            
            row = cursor.fetchone()
            progress[category] = {
                "total": row[0],
                "analyzed": row[1],
                "top_performers": row[2],
                "progress_pct": (row[1] / row[0] * 100) if row[0] > 0 else 0
            }
        
        return progress


async def run_continuous_analysis(
    universe: List[str],
    top_stocks_per_category: int = 5,
    calls_per_cycle: int = 15,
    cycle_interval_minutes: int = 60
) -> None:
    """Run continuous analysis with smart rotation."""
    
    engine = SmartRotationEngine(top_stocks_per_category)
    
    # Initialize universe
    await engine.initialize_universe(universe)
    
    print(f"\n🚀 Starting continuous analysis:")
    print(f"  📊 Top stocks per category: {top_stocks_per_category}")
    print(f"  🔄 Calls per cycle: {calls_per_cycle}")
    print(f"  ⏱️  Cycle interval: {cycle_interval_minutes} minutes")
    print(f"  📅 Refresh days: Monday, Wednesday, Friday")
    
    cycle_count = 0
    
    while True:
        try:
            cycle_count += 1
            print(f"\n{'='*60}")
            print(f"🔄 Analysis Cycle #{cycle_count} - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"{'='*60}")
            
            # Run analysis cycle
            results = await engine.run_analysis_cycle(calls_per_cycle)
            
            # Show progress
            progress = engine.get_analysis_progress()
            print(f"\n📈 Progress by Category:")
            for cat, stats in progress.items():
                print(f"  {cat}: {stats['analyzed']}/{stats['total']} ({stats['progress_pct']:.1f}%) analyzed")
            
            # Wait for next cycle
            print(f"\n⏱️  Waiting {cycle_interval_minutes} minutes until next cycle...")
            await asyncio.sleep(cycle_interval_minutes * 60)
            
        except KeyboardInterrupt:
            print("\n🛑 Analysis stopped by user")
            break
        except Exception as e:
            logger.error(f"Analysis cycle failed: {e}")
            print(f"❌ Cycle failed: {e}")
            print("⏱️  Waiting 5 minutes before retry...")
            await asyncio.sleep(300)


if __name__ == "__main__":
    # Test the rotation engine
    async def test_engine():
        from scheduler import _tickers
        universe = _tickers()[:100]  # Test with small universe
        
        engine = SmartRotationEngine(5)
        await engine.initialize_universe(universe)
        
        results = await engine.run_analysis_cycle(5)
        print(f"Test results: {results}")
    
    asyncio.run(test_engine())
