"""
Pure LLM-driven stock analysis system.
Zero momentum bias - LLM makes ALL investment decisions based on fundamentals + technicals.
"""

from __future__ import annotations
import asyncio
import json
import logging
import time
import random
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
import yfinance as yf
import pandas as pd
import numpy as np
from dataclasses import dataclass

# Logging setup
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class TechnicalIndicators:
    """Technical analysis indicators calculated from price data."""
    rsi_14: Optional[float] = None
    macd_signal: Optional[str] = None  # "bullish", "bearish", "neutral"
    bb_position: Optional[str] = None  # "upper", "middle", "lower"
    sma_20: Optional[float] = None
    sma_50: Optional[float] = None
    sma_200: Optional[float] = None
    volume_trend: Optional[str] = None  # "increasing", "decreasing", "stable"
    support_level: Optional[float] = None
    resistance_level: Optional[float] = None
    trend_direction: Optional[str] = None  # "uptrend", "downtrend", "sideways"


@dataclass
class LLMAnalysis:
    """Complete LLM analysis result."""
    ticker: str
    overall_score: float  # 0-100
    growth_score: float   # 0-100
    risk_score: float     # 0-100
    sentiment_score: float # 0-100
    recommendation: str   # BUY/HOLD/SELL
    advice: str          # Investment thesis
    target_price: Optional[float] = None
    analysis_date: datetime = None
    confidence: float = 100.0  # LLM confidence in analysis


def calculate_technical_indicators(ticker: str) -> TechnicalIndicators:
    """Calculate comprehensive technical indicators for a stock."""
    try:
        stock = yf.Ticker(ticker)
        hist = stock.history(period="1y")
        
        if hist.empty or len(hist) < 50:
            return TechnicalIndicators()
        
        close = hist['Close']
        volume = hist['Volume']
        high = hist['High']
        low = hist['Low']
        
        # RSI (14-day)
        rsi_14 = calculate_rsi(close, 14)
        
        # MACD Signal
        macd_signal = calculate_macd_signal(close)
        
        # Bollinger Bands Position
        bb_position = calculate_bollinger_position(close)
        
        # Moving Averages
        sma_20 = close.rolling(20).mean().iloc[-1] if len(close) >= 20 else None
        sma_50 = close.rolling(50).mean().iloc[-1] if len(close) >= 50 else None
        sma_200 = close.rolling(200).mean().iloc[-1] if len(close) >= 200 else None
        
        # Volume Trend
        volume_trend = calculate_volume_trend(volume)
        
        # Support/Resistance
        support_level, resistance_level = calculate_support_resistance(high, low, close)
        
        # Trend Direction
        trend_direction = calculate_trend_direction(close, sma_20, sma_50, sma_200)
        
        return TechnicalIndicators(
            rsi_14=rsi_14,
            macd_signal=macd_signal,
            bb_position=bb_position,
            sma_20=sma_20,
            sma_50=sma_50,
            sma_200=sma_200,
            volume_trend=volume_trend,
            support_level=support_level,
            resistance_level=resistance_level,
            trend_direction=trend_direction
        )
        
    except Exception as e:
        logger.warning(f"Failed to calculate technical indicators for {ticker}: {e}")
        return TechnicalIndicators()


def calculate_rsi(prices: pd.Series, period: int = 14) -> Optional[float]:
    """Calculate RSI indicator."""
    try:
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi.iloc[-1] if not rsi.empty else None
    except:
        return None


def calculate_macd_signal(prices: pd.Series) -> str:
    """Calculate MACD signal."""
    try:
        ema_12 = prices.ewm(span=12).mean()
        ema_26 = prices.ewm(span=26).mean()
        macd = ema_12 - ema_26
        signal = macd.ewm(span=9).mean()
        
        current_macd = macd.iloc[-1]
        current_signal = signal.iloc[-1]
        
        if current_macd > current_signal:
            return "bullish"
        elif current_macd < current_signal:
            return "bearish"
        else:
            return "neutral"
    except:
        return "neutral"


def calculate_bollinger_position(prices: pd.Series, period: int = 20) -> str:
    """Calculate Bollinger Bands position."""
    try:
        sma = prices.rolling(period).mean()
        std = prices.rolling(period).std()
        upper_band = sma + (std * 2)
        lower_band = sma - (std * 2)
        
        current_price = prices.iloc[-1]
        current_upper = upper_band.iloc[-1]
        current_lower = lower_band.iloc[-1]
        current_middle = sma.iloc[-1]
        
        if current_price >= current_upper:
            return "upper"
        elif current_price <= current_lower:
            return "lower"
        else:
            return "middle"
    except:
        return "middle"


def calculate_volume_trend(volume: pd.Series, period: int = 20) -> str:
    """Calculate volume trend."""
    try:
        recent_avg = volume.tail(5).mean()
        historical_avg = volume.tail(period).mean()
        
        if recent_avg > historical_avg * 1.2:
            return "increasing"
        elif recent_avg < historical_avg * 0.8:
            return "decreasing"
        else:
            return "stable"
    except:
        return "stable"


def calculate_support_resistance(high: pd.Series, low: pd.Series, close: pd.Series) -> Tuple[Optional[float], Optional[float]]:
    """Calculate support and resistance levels."""
    try:
        # Simple support/resistance based on recent highs and lows
        recent_data = pd.concat([high, low, close]).tail(50)
        support = recent_data.min()
        resistance = recent_data.max()
        return support, resistance
    except:
        return None, None


def calculate_trend_direction(close: pd.Series, sma_20: Optional[float], sma_50: Optional[float], sma_200: Optional[float]) -> str:
    """Determine overall trend direction."""
    try:
        current_price = close.iloc[-1]
        
        if sma_20 and sma_50 and sma_200:
            if current_price > sma_20 > sma_50 > sma_200:
                return "uptrend"
            elif current_price < sma_20 < sma_50 < sma_200:
                return "downtrend"
            else:
                return "sideways"
        else:
            # Fallback to simple price trend
            if len(close) >= 20:
                recent_trend = close.tail(20).pct_change().mean()
                if recent_trend > 0.01:
                    return "uptrend"
                elif recent_trend < -0.01:
                    return "downtrend"
                else:
                    return "sideways"
            else:
                return "sideways"
    except:
        return "sideways"


def create_comprehensive_llm_prompt(ticker: str, fundamental_data: Dict, technical_data: TechnicalIndicators) -> str:
    """Create comprehensive LLM prompt with fundamental + technical analysis."""
    
    tech_summary = f"""
TECHNICAL ANALYSIS:
- RSI (14-day): {technical_data.rsi_14:.1f if technical_data.rsi_14 else 'N/A'}
- MACD Signal: {technical_data.macd_signal}
- Bollinger Bands: Price at {technical_data.bb_position} band
- Moving Averages: 20-day: ${technical_data.sma_20:.2f if technical_data.sma_20 else 'N/A'}, 50-day: ${technical_data.sma_50:.2f if technical_data.sma_50 else 'N/A'}, 200-day: ${technical_data.sma_200:.2f if technical_data.sma_200 else 'N/A'}
- Volume Trend: {technical_data.volume_trend}
- Support Level: ${technical_data.support_level:.2f if technical_data.support_level else 'N/A'}
- Resistance Level: ${technical_data.resistance_level:.2f if technical_data.resistance_level else 'N/A'}
- Trend Direction: {technical_data.trend_direction}
"""
    
    return f"""
You are a professional CFA analyst. Analyze {ticker} using BOTH fundamental and technical analysis.

FUNDAMENTAL DATA:
{json.dumps(fundamental_data, indent=2, default=str)[:2000]}

{tech_summary}

ANALYSIS REQUIREMENTS:
1. OVERALL SCORE (0-100): Comprehensive investment attractiveness
2. GROWTH POTENTIAL (0-100): Future growth prospects and catalysts
3. RISK SCORE (0-100): Investment risk (100 = very risky, 0 = very safe)
4. SENTIMENT SCORE (0-100): Current market sentiment and momentum
5. RECOMMENDATION: BUY/HOLD/SELL
6. INVESTMENT ADVICE: 2-3 sentence thesis with key reasoning

Consider:
- Financial health (ratios, earnings, debt)
- Technical indicators and chart patterns
- Industry position and competitive advantages
- Market sentiment and analyst opinions
- Risk factors and potential catalysts
- Valuation metrics and fair value

Return ONLY this JSON format:
{{
    "overall_score": 85,
    "growth_score": 78,
    "risk_score": 35,
    "sentiment_score": 72,
    "recommendation": "BUY",
    "advice": "Strong fundamentals with bullish technical setup. RSI indicates oversold conditions with MACD turning positive. Solid earnings growth and expanding margins support higher valuation.",
    "target_price": 150.00,
    "confidence": 85
}}
"""


async def analyze_stock_with_llm(ticker: str, gemini_func) -> Optional[LLMAnalysis]:
    """Perform complete LLM analysis of a stock."""
    try:
        # Get fundamental data
        stock = yf.Ticker(ticker)
        info = stock.info or {}
        
        if not info.get('marketCap'):
            logger.debug(f"Skipping {ticker} - insufficient data")
            return None
        
        # Calculate technical indicators
        technical_data = calculate_technical_indicators(ticker)
        
        # Prepare fundamental data
        fundamental_data = {
            "basic_info": {
                "symbol": ticker,
                "company_name": info.get("longName", ""),
                "sector": info.get("sector", ""),
                "industry": info.get("industry", ""),
                "market_cap": info.get("marketCap", 0),
                "current_price": info.get("currentPrice", 0)
            },
            "financial_ratios": {
                "pe_ratio": info.get("trailingPE"),
                "forward_pe": info.get("forwardPE"),
                "price_to_book": info.get("priceToBook"),
                "roe": info.get("returnOnEquity"),
                "debt_to_equity": info.get("debtToEquity"),
                "current_ratio": info.get("currentRatio"),
                "profit_margin": info.get("profitMargins"),
                "revenue_growth": info.get("revenueGrowth")
            },
            "earnings_data": {
                "last_eps": info.get("trailingEps"),
                "forward_eps": info.get("forwardEps"),
                "earnings_growth": info.get("earningsGrowth")
            },
            "analyst_data": {
                "target_price": info.get("targetMeanPrice"),
                "recommendation": info.get("recommendationMean"),
                "number_of_analysts": info.get("numberOfAnalystOpinions")
            }
        }
        
        # Create comprehensive prompt
        prompt = create_comprehensive_llm_prompt(ticker, fundamental_data, technical_data)
        
        # Get LLM analysis
        response = gemini_func(prompt)
        
        # Parse response
        analysis_data = parse_llm_response(response)
        if not analysis_data:
            return None
        
        return LLMAnalysis(
            ticker=ticker,
            overall_score=analysis_data.get("overall_score", 50),
            growth_score=analysis_data.get("growth_score", 50),
            risk_score=analysis_data.get("risk_score", 50),
            sentiment_score=analysis_data.get("sentiment_score", 50),
            recommendation=analysis_data.get("recommendation", "HOLD"),
            advice=analysis_data.get("advice", "No analysis available"),
            target_price=analysis_data.get("target_price"),
            analysis_date=datetime.now(),
            confidence=analysis_data.get("confidence", 100)
        )
        
    except Exception as e:
        logger.error(f"Failed to analyze {ticker}: {e}")
        return None


def parse_llm_response(response: str) -> Optional[Dict]:
    """Parse LLM JSON response."""
    try:
        # Handle markdown-wrapped JSON
        import re
        json_match = re.search(r'```json\s*(.*?)\s*```', response, re.DOTALL)
        if json_match:
            return json.loads(json_match.group(1))
        else:
            return json.loads(response)
    except Exception as e:
        logger.error(f"Failed to parse LLM response: {e}")
        return None


async def test_system():
    """Test the pure LLM system."""
    print("🧪 Testing Pure LLM Analysis System")

    # Test technical analysis
    print("\n📊 Testing Technical Analysis...")
    indicators = calculate_technical_indicators("AAPL")
    print(f"AAPL Technical Indicators:")
    print(f"  RSI: {indicators.rsi_14}")
    print(f"  MACD: {indicators.macd_signal}")
    print(f"  Trend: {indicators.trend_direction}")
    print(f"  Support: ${indicators.support_level}")
    print(f"  Resistance: ${indicators.resistance_level}")


if __name__ == "__main__":
    asyncio.run(test_system())
