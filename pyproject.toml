[project]
name = "financialstockmanager"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "agno>=1.6.2",
    "aiohttp>=3.12.13",
    "apscheduler>=3.11.0",
    "dotenv>=0.9.9",
    "feedparser>=6.0.11",
    "google-generativeai>=0.8.5",
    "llama-index>=0.12.42",
    "pandas>=2.3.0",
    "qdrant-client>=1.14.2",
    "requests>=2.32.4",
    "snscrape>=0.7.0.20230622",
    "sqlalchemy>=2.0.41",
    "streamlit>=1.45.1",
    "vadersentiment>=3.3.2",
    "watchdog>=6.0.0",
    "yfinance>=0.2.63",
]
