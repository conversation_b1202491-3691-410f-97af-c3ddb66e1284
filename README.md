# Stock Analyst Pro v2.0 - Enhanced LLM Edition 🧠

**🚀 NEW v2.0:** Revolutionary **Pure LLM Analysis System** with zero momentum bias! LLM makes ALL investment decisions based on comprehensive fundamental + technical analysis.

A Gemini-powered, command-line assistant for retail investors with THREE analysis approaches:

## 🎯 Choose Your Analysis System

| System | Philosophy | Best For | Command |
|--------|------------|----------|---------|
| **🧠 Pure LLM** | Zero momentum bias - LLM analyzes ALL stocks using fundamentals + technicals | Investors who believe "past performance doesn't predict future" | `python stock_agent_v2.py pure-llm analyze` |
| **⚡ Enhanced Scan** | Hybrid - Momentum screening + LLM validation | Balanced approach combining momentum with AI | `python stock_agent_v2.py enhanced-scan` |
| **📊 Legacy Scan** | Traditional momentum + basic LLM scoring | Traditional momentum investors | `python stock_agent_v2.py scan` |

## 🧠 Pure LLM System (RECOMMENDED)

**What it does:** Eliminates historical bias completely. LLM makes ALL investment decisions based on current fundamentals, technical indicators, earnings, industry position, and market sentiment.

### Key Features
- ✅ **Zero momentum calculations** - No historical price bias
- ✅ **4-component scoring** - Overall, Growth, Risk, Sentiment (0-100 each)
- ✅ **Technical analysis** - RSI, MACD, Bollinger Bands, Moving Averages
- ✅ **Smart rotation** - Ensures all 11,464 stocks get analyzed eventually
- ✅ **Dual dashboard** - Top performers + all analyzed stocks (Port 8502)

### Commands & Arguments

| Command | Arguments | Default | Description |
|---------|-----------|---------|-------------|
| `pure-llm analyze` | `--max_calls` | 15 | LLM calls per cycle |
| | `--top_stocks_per_category` | 5 | Top stocks to track per category |
| | `--continuous` | False | Run continuous analysis |
| | `--cycle_interval` | 60 | Minutes between cycles |
| `pure-llm status` | None | - | Show analysis progress and rate limits |
| `pure-llm dashboard` | None | - | Launch dashboard at http://localhost:8502 |

### Quick Start
```bash
# Setup
export GEMINI_API_KEY=your_key_here
pip install yfinance pandas google-generativeai streamlit numpy

# Run analysis
python stock_agent_v2.py pure-llm analyze --max_calls 15 --top_stocks_per_category 5
python stock_agent_v2.py pure-llm status
python stock_agent_v2.py pure-llm dashboard

# Continuous mode
python stock_agent_v2.py pure-llm analyze --continuous --cycle_interval 60
```

## ⚡ Enhanced Scan System (HYBRID)

**What it does:** Uses momentum screening to find top performers, then sends them to LLM for comprehensive analysis and validation.

### Commands & Arguments

| Command | Arguments | Default | Description |
|---------|-----------|---------|-------------|
| `enhanced-scan` | `--max_stocks` | 5 | Stocks per category to analyze |
| | `--max_concurrent` | 1 | Concurrent LLM calls |
| | `--categories` | all | Categories: stable,emerging,pharma,moon |
| | `--show_limits` | False | Show rate limits and exit |

### Quick Start
```bash
# Hybrid analysis
python stock_agent_v2.py enhanced-scan --max_stocks 5 --max_concurrent 1

# Specific categories
python stock_agent_v2.py enhanced-scan --categories stable emerging

# Check limits
python stock_agent_v2.py enhanced-scan --show_limits
```

## 📊 Legacy System (TRADITIONAL)

**What it does:** Traditional momentum-based analysis with basic LLM scoring. Original system behavior.

### Commands & Arguments

| Command | Arguments | Default | Description |
|---------|-----------|---------|-------------|
| `scan` | None | - | Run continuous momentum scanner |
| `serve` | None | - | Launch dashboard at http://localhost:8501 |
| `analyse` | `TICKER` | - | Analyze individual stock |
| `plan` | `TICKER AMOUNT` | - | Generate investment plan |
| | `--dip_pct` | auto | Custom dip percentage |
| | `--stop_pct` | auto | Custom stop-loss percentage |
| `setup` | None | - | Risk profile questionnaire |
| `discover` | None | - | Find high-scoring stocks |
| `chat` | None | - | Interactive REPL |

### Quick Start
```bash
# Traditional analysis
python stock_agent_v2.py scan &           # Keep running
python stock_agent_v2.py serve            # Dashboard

# Individual analysis
python stock_agent_v2.py analyse AAPL
python stock_agent_v2.py plan AAPL 1000
```

## 🔧 Installation

```bash
# Create virtual environment
python -m venv venv && source venv/bin/activate

# Install dependencies
pip install yfinance pandas google-generativeai streamlit numpy scikit-learn

# Set API key
export GEMINI_API_KEY="your_gemini_api_key_here"
```

## 🛡️ Rate Limiting (Gemini Free Tier)

The system is optimized for Gemini's free tier limits:
- **Daily**: 1,500 requests per day
- **Per-minute**: 15 requests per minute
- **Delays**: 4.5 seconds between calls

### Rate Limiting Utilities
```bash
python rate_limit_config.py status        # Check quota
python rate_limit_config.py suggest       # Get recommendations
python rate_limit_config.py estimate 4 5  # Estimate calls needed
```

## 🤔 Which System Should You Choose?

**Choose Pure LLM if you:**
- ✅ Believe "past performance doesn't predict future results"
- ✅ Want comprehensive fundamental analysis
- ✅ Want ALL stocks analyzed eventually (not just momentum winners)

**Choose Enhanced Scan if you:**
- ✅ Want momentum screening + AI validation
- ✅ Prefer faster results with fewer LLM calls
- ✅ Like balanced traditional + AI approach

**Choose Legacy Scan if you:**
- ✅ Prefer traditional momentum-based analysis
- ✅ Want the original system behavior

## 📊 System Comparison

| Aspect | Pure LLM | Enhanced Scan | Legacy Scan |
|--------|----------|---------------|-------------|
| **Analysis Method** | Pure fundamental + technical | Momentum + LLM validation | Momentum + basic LLM |
| **Stocks Covered** | All 11,464 eventually | Top momentum picks only | Top momentum picks only |
| **Dashboard Port** | 8502 | 8501 | 8501 |
| **Database** | `pure_llm_stocks.db` | `enhanced_stocks.db` | `market.db` |
| **Historical Bias** | Zero | Some | High |

## 🎯 TL;DR - Quick Commands

**Pure LLM (Recommended):**
```bash
python stock_agent_v2.py pure-llm analyze --max_calls 15
python stock_agent_v2.py pure-llm dashboard
```

**Enhanced Scan (Hybrid):**
```bash
python stock_agent_v2.py enhanced-scan --max_stocks 5
```

**Legacy Scan (Traditional):**
```bash
python stock_agent_v2.py scan &
python stock_agent_v2.py serve
```

---

© 2025 Stock Analyst Pro v2.0 - Enhanced LLM Edition
