"""
Enhanced database schema for comprehensive LLM analysis storage.
"""

import sqlite3
import json
import time
from pathlib import Path
from typing import Any, Dict

DB_PATH = Path.home() / "enhanced_market.db"

# Enhanced schema with comprehensive analysis fields
_ENHANCED_SCHEMA = """
PRAGMA journal_mode=WAL;

-- Enhanced stocks table with comprehensive analysis
CREATE TABLE IF NOT EXISTS enhanced_stocks (
  ticker                TEXT PRIMARY KEY,
  category              TEXT,
  recommendation        TEXT,  -- BUY/HOLD/SELL
  conviction_score      REAL,  -- 0-100
  target_price          REAL,
  current_price         REAL,
  
  -- Financial Analysis
  financial_health_score REAL,  -- 0-100
  pe_ratio              REAL,
  forward_pe            REAL,
  roe                   REAL,
  debt_to_equity        REAL,
  current_ratio         REAL,
  profit_margin         REAL,
  
  -- Technical Analysis
  technical_score       REAL,  -- 0-100
  support_level         REAL,
  resistance_level      REAL,
  sma_20                REAL,
  sma_50                REAL,
  sma_200               REAL,
  
  -- Fundamental Analysis
  earnings_quality      TEXT,
  revenue_trend         TEXT,
  industry_position     TEXT,
  
  -- Market Sentiment
  sentiment_score       REAL,  -- -1 to +1
  analyst_consensus     TEXT,
  recent_news_impact    TEXT,
  
  -- Investment Thesis
  investment_thesis     TEXT,
  key_risks            TEXT,  -- JSON array as string
  key_catalysts        TEXT,  -- JSON array as string
  
  -- Metadata
  last_checked         INTEGER,
  analysis_date        INTEGER,
  data_sources         TEXT,  -- JSON array as string
  
  -- Legacy compatibility fields
  score                REAL,   -- Maps to conviction_score
  price_now            REAL,   -- Maps to current_price
  roi_window           TEXT,   -- Set to "LLM" for enhanced analysis
  potential_gain       REAL,   -- Calculated from target_price
  llm_opinion          TEXT,   -- Summary of recommendation + thesis
  risk                 TEXT    -- Derived from conviction_score
);

-- Enhanced plans table with detailed investment strategies
CREATE TABLE IF NOT EXISTS enhanced_plans (
  ticker              TEXT,
  plan_type           TEXT,  -- "enhanced" | "basic"
  recommendation      TEXT,
  target_price        REAL,
  conviction_level    TEXT,
  position_pct        REAL,
  entry_strategy      TEXT,  -- JSON
  risk_management     TEXT,  -- JSON
  monitoring_plan     TEXT,  -- JSON
  plan_json           TEXT,  -- Full plan details
  created_at          INTEGER
);

-- Analysis history for tracking changes over time
CREATE TABLE IF NOT EXISTS analysis_history (
  id                  INTEGER PRIMARY KEY AUTOINCREMENT,
  ticker              TEXT,
  recommendation      TEXT,
  conviction_score    REAL,
  target_price        REAL,
  financial_health    REAL,
  technical_score     REAL,
  sentiment_score     REAL,
  investment_thesis   TEXT,
  analysis_date       INTEGER,
  
  FOREIGN KEY (ticker) REFERENCES enhanced_stocks (ticker)
);

-- Enhanced news table with sentiment analysis
CREATE TABLE IF NOT EXISTS enhanced_news (
  id                  INTEGER PRIMARY KEY AUTOINCREMENT,
  ticker              TEXT,
  headline            TEXT,
  summary             TEXT,
  sentiment_score     REAL,  -- -1 to +1
  impact_score        REAL,  -- 0-100 (how much this affects stock)
  source_url          TEXT,
  source_name         TEXT,
  published_at        INTEGER,
  analyzed_at         INTEGER,
  
  FOREIGN KEY (ticker) REFERENCES enhanced_stocks (ticker)
);

-- Analyst ratings and targets
CREATE TABLE IF NOT EXISTS analyst_data (
  id                  INTEGER PRIMARY KEY AUTOINCREMENT,
  ticker              TEXT,
  analyst_firm        TEXT,
  rating              TEXT,  -- BUY/HOLD/SELL
  target_price        REAL,
  previous_rating     TEXT,
  previous_target     REAL,
  rating_date         INTEGER,
  
  FOREIGN KEY (ticker) REFERENCES enhanced_stocks (ticker)
);

-- Performance tracking
CREATE TABLE IF NOT EXISTS performance_tracking (
  id                  INTEGER PRIMARY KEY AUTOINCREMENT,
  ticker              TEXT,
  recommendation_date INTEGER,
  recommendation      TEXT,
  target_price        REAL,
  entry_price         REAL,
  current_price       REAL,
  actual_return       REAL,  -- Percentage return
  days_held           INTEGER,
  status              TEXT,   -- "active" | "closed" | "stopped"
  
  FOREIGN KEY (ticker) REFERENCES enhanced_stocks (ticker)
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_enhanced_stocks_conviction ON enhanced_stocks(conviction_score DESC);
CREATE INDEX IF NOT EXISTS idx_enhanced_stocks_category ON enhanced_stocks(category);
CREATE INDEX IF NOT EXISTS idx_enhanced_stocks_recommendation ON enhanced_stocks(recommendation);
CREATE INDEX IF NOT EXISTS idx_analysis_history_ticker_date ON analysis_history(ticker, analysis_date DESC);
CREATE INDEX IF NOT EXISTS idx_enhanced_news_ticker_date ON enhanced_news(ticker, published_at DESC);
"""

_conn: sqlite3.Connection | None = None


def get_enhanced_connection() -> sqlite3.Connection:
    """Get enhanced database connection with comprehensive schema."""
    global _conn
    if _conn is None:
        _conn = sqlite3.connect(DB_PATH, check_same_thread=False)
        _conn.row_factory = sqlite3.Row  # Enable dict-like access
    
    # Ensure enhanced schema exists
    with _conn:
        _conn.executescript(_ENHANCED_SCHEMA)
    
    return _conn


def upsert_enhanced_stock(record: Dict[str, Any]) -> None:
    """Insert or update enhanced stock analysis."""
    conn = get_enhanced_connection()
    
    # Prepare the record
    cols = ", ".join(record.keys())
    vals = ":" + ", :".join(record.keys())
    
    # Create upsert SQL
    sql = (
        f"INSERT INTO enhanced_stocks ({cols}) VALUES ({vals}) "
        f"ON CONFLICT(ticker) DO UPDATE SET "
        + ", ".join(f"{c}=excluded.{c}" for c in record.keys() if c != "ticker")
    )
    
    with conn:
        conn.execute(sql, record)


def add_analysis_history(ticker: str, analysis_data: Dict[str, Any]) -> None:
    """Add analysis to history for tracking changes."""
    conn = get_enhanced_connection()
    
    history_record = {
        "ticker": ticker,
        "recommendation": analysis_data.get("recommendation"),
        "conviction_score": analysis_data.get("conviction_score"),
        "target_price": analysis_data.get("target_price"),
        "financial_health": analysis_data.get("financial_health_score"),
        "technical_score": analysis_data.get("technical_score"),
        "sentiment_score": analysis_data.get("sentiment_score"),
        "investment_thesis": analysis_data.get("investment_thesis"),
        "analysis_date": int(time.time())
    }
    
    with conn:
        conn.execute("""
            INSERT INTO analysis_history 
            (ticker, recommendation, conviction_score, target_price, 
             financial_health, technical_score, sentiment_score, 
             investment_thesis, analysis_date)
            VALUES (:ticker, :recommendation, :conviction_score, :target_price,
                    :financial_health, :technical_score, :sentiment_score,
                    :investment_thesis, :analysis_date)
        """, history_record)


def get_top_stocks_by_conviction(limit: int = 50) -> List[Dict[str, Any]]:
    """Get top stocks ranked by conviction score."""
    conn = get_enhanced_connection()
    
    cursor = conn.execute("""
        SELECT * FROM enhanced_stocks 
        WHERE conviction_score IS NOT NULL
        ORDER BY conviction_score DESC 
        LIMIT ?
    """, (limit,))
    
    return [dict(row) for row in cursor.fetchall()]


def get_stocks_by_recommendation(recommendation: str, min_conviction: float = 70) -> List[Dict[str, Any]]:
    """Get stocks by recommendation with minimum conviction."""
    conn = get_enhanced_connection()
    
    cursor = conn.execute("""
        SELECT * FROM enhanced_stocks 
        WHERE recommendation = ? AND conviction_score >= ?
        ORDER BY conviction_score DESC
    """, (recommendation, min_conviction))
    
    return [dict(row) for row in cursor.fetchall()]


def get_category_analysis(category: str) -> List[Dict[str, Any]]:
    """Get all stocks in a category with their analysis."""
    conn = get_enhanced_connection()
    
    cursor = conn.execute("""
        SELECT * FROM enhanced_stocks 
        WHERE category = ?
        ORDER BY conviction_score DESC
    """, (category,))
    
    return [dict(row) for row in cursor.fetchall()]


def get_analysis_summary() -> Dict[str, Any]:
    """Get summary statistics of the analysis."""
    conn = get_enhanced_connection()
    
    cursor = conn.execute("""
        SELECT 
            COUNT(*) as total_stocks,
            AVG(conviction_score) as avg_conviction,
            COUNT(CASE WHEN recommendation = 'BUY' THEN 1 END) as buy_count,
            COUNT(CASE WHEN recommendation = 'HOLD' THEN 1 END) as hold_count,
            COUNT(CASE WHEN recommendation = 'SELL' THEN 1 END) as sell_count,
            AVG(CASE WHEN recommendation = 'BUY' THEN conviction_score END) as avg_buy_conviction,
            COUNT(CASE WHEN conviction_score >= 80 THEN 1 END) as high_conviction_count
        FROM enhanced_stocks
    """)
    
    return dict(cursor.fetchone())


if __name__ == "__main__":
    # Test the enhanced database
    conn = get_enhanced_connection()
    print("✅ Enhanced database schema created successfully")
    
    # Show table info
    cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = [row[0] for row in cursor.fetchall()]
    print(f"📊 Created tables: {', '.join(tables)}")
