"""
Endless market-scanner.  Start with:  python -m scan_loop run
"""

from __future__ import annotations
import asyncio, signal, sys, time, json
from pathlib import Path
from typing import Tuple, Dict, List
import yfinance as yf

# ---------------------------------------------------------------------------
# Logging setup: everything recorded to ~/scan.log + echoed to console
# ---------------------------------------------------------------------------
import logging
LOG_FILE = Path.home() / "scan.log"

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s %(levelname)s: %(message)s",
    handlers=[
        logging.FileHandler(LOG_FILE, encoding="utf-8"),
        logging.StreamHandler(sys.stdout),
    ],
)
logging.info("=== scan_loop.py started ===")

from scheduler import schedule_all
from fetchers import fetch_all
from scoring import classify, roi_change, stock_score
from llm import ask_gemini_batch
from db import upsert_stock
from stock_agent_v2 import gemini_ask

SUMMARY = Path.home() / ".scan_summary.txt"


def normalize_potential_gain(value) -> float:
    """Convert potential_gain to a consistent float value (percentage)."""
    if value is None or value == "N/A":
        return None

    if isinstance(value, (int, float)):
        return float(value)

    # Handle string values
    str_val = str(value).strip()
    if not str_val or str_val.lower() in ['none', 'n/a', 'null', 'unknown']:
        return None

    # Extract numeric value from strings like "15%", "7-12%", "Uncertain, -20% to +40%"
    import re

    # Look for patterns like "15%", "15.5%"
    simple_match = re.search(r'(\d+(?:\.\d+)?)%', str_val)
    if simple_match:
        return float(simple_match.group(1))

    # Look for range patterns like "7-12%", "-20% to +40%"
    range_match = re.search(r'(-?\d+(?:\.\d+)?)%?\s*(?:to|-)\s*\+?(-?\d+(?:\.\d+)?)%', str_val)
    if range_match:
        low, high = float(range_match.group(1)), float(range_match.group(2))
        return (low + high) / 2  # Return average of range

    # Look for single numbers without %
    number_match = re.search(r'(-?\d+(?:\.\d+)?)', str_val)
    if number_match:
        return float(number_match.group(1))

    # If all else fails, return None
    return None


def extract_json_from_response(response: str) -> str:
    """Extract JSON from LLM response that might be wrapped in markdown code blocks."""
    response = response.strip()

    # If response starts with ```json, extract the JSON content
    if response.startswith("```json"):
        # Find the end of the code block
        end_marker = response.find("```", 7)  # Start searching after "```json"
        if end_marker != -1:
            return response[7:end_marker].strip()

    # If response starts with ```, try to extract JSON
    elif response.startswith("```"):
        # Find the end of the code block
        end_marker = response.find("```", 3)
        if end_marker != -1:
            return response[3:end_marker].strip()

    # Look for JSON embedded in text (like AAPL case)
    json_start = response.find("```json")
    if json_start != -1:
        json_start += 7  # Skip "```json"
        json_end = response.find("```", json_start)
        if json_end != -1:
            return response[json_start:json_end].strip()

    # If no markdown formatting, return as-is
    return response


async def _process(t: str, cat: str) -> Tuple[str, Dict, Dict]:
    try:
        data = await fetch_all(t)
        close, info = data["close"], data["info"]
        sent = data.get("sentiment", 0.0)

        # Check if we have valid price data
        if close.empty:
            # Skip stocks with no price data
            raise ValueError(f"No price data available for {t}")

        m = {"stable": 12, "emerging": 6, "pharma": 6, "moon": 3}[cat]
        price_chg = roi_change(close, m * 21)
        score = stock_score(price_chg, sent)

        record = {
            "category": cat,
            "risk": {"stable": "Low", "emerging": "Medium", "pharma": "Medium", "moon": "High"}[cat],
            "score": score,
            "last_checked": int(time.time()),
            "price_now": close.iloc[-1],  # Safe to access now since we checked close.empty
            "roi_window": f"{m}m",
            "potential_gain": None,
            "llm_opinion": None,
        }
        return t, record, data

    except Exception as e:
        # Log the error and re-raise to be handled by the caller
        logging.debug(f"Failed to process {t}: {e}")
        raise


async def _worker(queue: asyncio.Queue):
    while True:
        cat, universe = await queue.get()

        target = [t for t in universe if classify(yf.Ticker(t).info) == cat][:150]
        print(f"🎯 [{cat}] Found {len(target)} stocks: {', '.join(target[:10])}{'...' if len(target) > 10 else ''}")

        # Process stocks with error handling
        batch = []
        for t in target:
            try:
                result = await _process(t, cat)
                batch.append(result)
            except Exception as e:
                logging.debug(f"Skipping {t} in {cat} category: {e}")
                continue

        top = sorted(batch, key=lambda r: r[1]["score"], reverse=True)[:25]
        top_tickers = [t for t, _, _ in top]
        print(f"   Analyzing top {len(top)} performers: {', '.join(top_tickers[:5])}{'...' if len(top_tickers) > 5 else ''}")

        if top:
            print(f"   🧠 Sending {len(top)} stocks to LLM for analysis...")
            prompts = [
                f"You are a CFA. Analyse {t} with this JSON {json.dumps(d, default=str)[:4000]} "
                "Return JSON {potential_gain_percent, opinion (BUY/SELL/HOLD), rationale, buy_plan}."
                for t, _, d in top
            ]
            tickers = [t for t, _, _ in top]

            try:
                llm_out = await ask_gemini_batch(tickers, prompts, gemini_ask)
                llm_map = dict(llm_out)
                print(f"   ✅ Received LLM analysis for {len(llm_out)} stocks")
            except Exception as e:
                print(f"   ❌ LLM analysis failed: {e}")
                llm_map = {}
        else:
            print(f"   ⚠️  No stocks to analyze (all failed processing)")
            llm_map = {}

        new = []
        analyzed_count = 0
        for (t, rec, _), reply in zip(top, [llm_map.get(k, "") for k in tickers]):
            if not reply:
                continue

            try:
                # Extract JSON from potentially markdown-wrapped response
                json_content = extract_json_from_response(reply)
                js = json.loads(json_content)
                analyzed_count += 1
                opinion = js.get("opinion", "UNKNOWN")

                # Normalize potential_gain to consistent float
                raw_gain = js.get("potential_gain_percent")
                rec["potential_gain"] = normalize_potential_gain(raw_gain)
                rec["llm_opinion"] = f"{opinion} – {js.get('rationale', '')}"
                upsert_stock({"ticker": t, **rec})

                if opinion.upper() == "BUY" and rec["score"] > 50:
                    new.append(t)

            except Exception:
                continue

        print(f"   📈 Successfully analyzed {analyzed_count}/{len(top)} stocks")

        out = f"[{cat}] cycle complete. New BUYs: {', '.join(new) if new else 'none'}"
        print(out)
        logging.info(out)
        # Append to summary file
        with open(SUMMARY, "a") as f:
            f.write(out + "\n")
        queue.task_done()


async def run_once():
    """Run a single scan cycle for all categories and exit."""
    print("🔍 Running single scan cycle...")

    # Get universe of tickers
    from scheduler import _tickers
    universe = _tickers()
    print(f"📊 Universe size: {len(universe)}")

    # Process each category once
    categories = ["stable", "emerging", "pharma", "moon"]

    for cat in categories:
        print(f"\n🎯 Processing category: {cat}")
        try:
            # Process this category with error handling
            target = []
            for t in universe[:200]:  # Limit universe size for faster execution
                try:
                    ticker_info = yf.Ticker(t).info
                    if classify(ticker_info) == cat:
                        target.append(t)
                        if len(target) >= 20:  # Limit to 20 stocks per category for faster execution
                            break
                except Exception:
                    continue  # Skip problematic tickers

            print(f"   Found {len(target)} {cat} stocks: {', '.join(target)}")

            if not target:
                print(f"   No stocks found for category {cat}")
                continue

            # Process stocks with error handling
            batch = []
            for t in target:
                try:
                    result = await _process(t, cat)
                    batch.append(result)
                except Exception as e:
                    logging.debug(f"Skipping {t} in {cat} category: {e}")
                    continue

            top = sorted(batch, key=lambda r: r[1]["score"], reverse=True)[:10]  # Top 10 for faster execution
            top_tickers = [t for t, _, _ in top]
            print(f"   Analyzing top {len(top)} stocks: {', '.join(top_tickers)}")

            if top:
                print(f"   🧠 Sending {len(top)} stocks to LLM for analysis...")
                prompts = [
                    f"You are a CFA. Analyse {t} with this JSON {json.dumps(d, default=str)[:2000]} "
                    "Return JSON {potential_gain_percent, opinion (BUY/SELL/HOLD), rationale}."
                    for t, _, d in top
                ]
                tickers = [t for t, _, _ in top]

                try:
                    llm_out = await ask_gemini_batch(tickers, prompts, gemini_ask)
                    llm_map = dict(llm_out)
                    print(f"   ✅ Received LLM analysis for {len(llm_out)} stocks")
                except Exception as e:
                    print(f"   ❌ LLM analysis failed: {e}")
                    llm_map = {}
            else:
                print(f"   ⚠️  No stocks to analyze (all failed processing)")
                llm_map = {}

            new_buys = []
            analyzed_count = 0
            for (t, rec, _), reply in zip(top, [llm_map.get(k, "") for k in tickers]):
                if not reply:
                    print(f"     ⚠️  No LLM response for {t}")
                    continue

                try:
                    # Extract JSON from potentially markdown-wrapped response
                    json_content = extract_json_from_response(reply)
                    js = json.loads(json_content)
                    analyzed_count += 1

                    opinion = js.get("opinion", "UNKNOWN")
                    rationale = js.get("rationale", "No rationale provided")
                    potential_gain = js.get("potential_gain_percent", "N/A")

                    print(f"     📊 {t}: {opinion} (Gain: {potential_gain}%) - {rationale[:50]}...")

                    # Normalize potential_gain to consistent float
                    rec["potential_gain"] = normalize_potential_gain(potential_gain)
                    rec["llm_opinion"] = f"{opinion} – {rationale}"
                    upsert_stock({"ticker": t, **rec})

                    if opinion.upper() == "BUY" and rec["score"] > 50:
                        new_buys.append(t)
                        print(f"     🎯 {t} added to BUY list!")

                except Exception as e:
                    print(f"     ❌ Failed to parse LLM response for {t}: {e}")
                    print(f"     🔍 First 200 chars: '{reply[:200]}...'")
                    continue

            print(f"   📈 Successfully analyzed {analyzed_count}/{len(top)} stocks")

            result = f"[{cat}] scan complete. New BUYs: {', '.join(new_buys) if new_buys else 'none'}"
            print(f"   {result}")
            logging.info(result)
            # Append to summary file
            with open(SUMMARY, "a") as f:
                f.write(result + "\n")

        except Exception as e:
            error_msg = f"[{cat}] scan failed: {e}"
            print(f"   ❌ {error_msg}")
            logging.error(error_msg)

    print("\n✅ Single scan cycle completed!")


async def main():
    """Run continuous scanning with scheduler."""
    q = asyncio.Queue()
    sched = schedule_all(q)
    worker = asyncio.create_task(_worker(q))
    stop = asyncio.Event()

    def _sig(*_): stop.set()
    loop = asyncio.get_event_loop()
    loop.add_signal_handler(signal.SIGINT, _sig)

    async def _stdin():
        while not stop.is_set():
            if (ln := await asyncio.to_thread(sys.stdin.readline)).strip().lower() == "q":
                stop.set()

    await asyncio.gather(stop.wait(), _stdin())
    sched.shutdown()
    worker.cancel()
    print("Scanner stopped.")


if __name__ == "__main__":
    if len(sys.argv) == 2:
        if sys.argv[1] == "run":
            asyncio.run(main())
        elif sys.argv[1] == "run_once":
            asyncio.run(run_once())
        else:
            print("Usage: python -m scan_loop [run|run_once]")
    else:
        print("Usage: python -m scan_loop [run|run_once]")