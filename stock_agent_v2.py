#!/usr/bin/env python3
"""
Stock Analyst Pro – Gemini‑Only Edition (v 1.2)
============================================
A fully self‑contained command‑line agent built with **Agno** that analyses
stocks, remembers user context, ingests documents, and produces actionable
investment plans.  
**New in this release**: *adaptive* DCA/stop logic that chooses sensible price
levels when the user does not specify them.
"""
from __future__ import annotations

import argparse
import json
import logging
import os
import sys
import subprocess
from dataclasses import dataclass
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple
from db import add_plan

import pandas as pd
import yfinance as yf

from dotenv import load_dotenv

load_dotenv(override=True)

# ---------------------------------------------------------------------------
# Enhanced LLM Analysis Framework (v2.0)
# ---------------------------------------------------------------------------

# Market context and macro analysis
MARKET_INDICATORS = [
    "^GSPC",  # S&P 500
    "^VIX",   # Volatility Index
    "^TNX",   # 10-Year Treasury
    "DXY",    # Dollar Index
]

# Investment analysis frameworks
ANALYSIS_FRAMEWORKS = {
    "business_quality": [
        "competitive_advantages", "management_quality", "industry_dynamics",
        "revenue_sustainability", "margin_trends", "capital_allocation"
    ],
    "valuation": [
        "dcf_analysis", "comparable_multiples", "asset_based_valuation",
        "fair_value_estimate", "valuation_drivers", "sensitivity_analysis"
    ],
    "risk_assessment": [
        "company_risks", "industry_risks", "macro_risks",
        "downside_scenarios", "risk_mitigation", "position_sizing"
    ],
    "investment_thesis": [
        "key_catalysts", "timeline", "upside_potential",
        "competitive_positioning", "execution_risk", "exit_strategy"
    ]
}

# ---------------------------------------------------------------------------
# Optional heavy dependencies (all degrade gracefully when missing)
# ---------------------------------------------------------------------------
try:
    from agno.agent import Agent
    from agno.memory import FileMemory, QdrantMemory
    from agno.workflow import TaskGraph, task
except ImportError:  # pragma: no cover
    Agent = None  # type: ignore
    TaskGraph = None  # type: ignore

try:
    from llama_index import VectorStoreIndex, StorageContext
    from llama_index.readers import SimpleDirectoryReader
    from llama_index.vector_stores.qdrant import QdrantVectorStore
except ImportError:  # pragma: no cover
    VectorStoreIndex = None  # type: ignore

try:
    from vaderSentiment.vaderSentiment import SentimentIntensityAnalyzer
    _VADER = SentimentIntensityAnalyzer()
except ImportError:  # pragma: no cover
    _VADER = None

try:
    import google.generativeai as genai
except ImportError:  # pragma: no cover
    genai = None

# ---------------------------------------------------------------------------
# Configuration & logging
# ---------------------------------------------------------------------------
PROFILE_FILE = Path.home() / ".stock_agent_profile.json"
MEM_PATH     = Path.home() / ".stock_agent_memory.json"
KB_PATH      = Path.home() / "stock_agent_kb"

DEFAULT_TICKERS = [
    "AAPL", "MSFT", "AMZN", "GOOGL", "META", "TSLA", "NVDA",
]

logging.basicConfig(level=logging.INFO, format="%(levelname)s: %(message)s")
log = logging.getLogger("stock_agent")

# ---------------------------------------------------------------------------
# Gemini helper & Agno‑compatible chat adapter
# ---------------------------------------------------------------------------

def _init_gemini() -> None:
    if genai is None:
        raise RuntimeError("google‑generativeai not installed – `pip install google-generativeai`.  ")
    key = os.environ.get("GOOGLE_API_KEY")
    if not key:
        raise RuntimeError("Set GEMINI_API_KEY environment variable.")
    genai.configure(api_key=key, transport="rest")


def gemini_ask(prompt: str, *, system: str | None = None) -> str:
    _init_gemini()
    model_name = os.environ.get("GEMINI_MODEL", "models/gemini-2.0-flash")
    model = genai.GenerativeModel(model_name)
    full_prompt = f"{system}\n{prompt}" if system else prompt
    try:
        return model.generate_content(full_prompt).text.strip()
    except Exception as e:
        print("Gemini error:", e)
        return "[Gemini quota error]"


class GeminiChat:
    """Minimal adapter so Agno can call Gemini like an OpenAI‑style chat model."""

    def __init__(self, *, temperature: float = 0.2):
        _init_gemini()
        model_name = os.environ.get("GEMINI_MODEL", "models/gemini-2.0-flash")
        self._model = genai.GenerativeModel(model_name)
        self.temperature = temperature

    def chat(self, messages: List[Dict[str, str]]) -> str:  # noqa: D401
        convo = "\n".join(f"{m['role'].capitalize()}: {m['content']}" for m in messages)
        reply = self._model.generate_content(
            convo,
            generation_config={"temperature": self.temperature},
        )
        return reply.text.strip()

# ---------------------------------------------------------------------------
# Risk‑profile dataclass & interactive quiz
# ---------------------------------------------------------------------------
@dataclass
class RiskProfile:
    level: str  # "Low" | "Medium" | "High"

    def save(self) -> None:
        PROFILE_FILE.write_text(json.dumps({"risk": self.level}, indent=2))
        log.info("Saved risk profile: %s", self.level)

    @classmethod
    def load(cls) -> "RiskProfile":
        if PROFILE_FILE.exists():
            lvl = json.loads(PROFILE_FILE.read_text()).get("risk", "Medium")
            return cls(lvl)
        return cls("Medium")


def risk_quiz() -> str:
    score = 0
    horizon = input("Investment horizon (Short / Medium / Long): ").lower()
    score += 0 if horizon.startswith("s") else 1 if horizon.startswith("m") else 2

    drawdown = input("If your portfolio fell 20 % tomorrow, you would (Sell / Hold / Buy more): ").lower()
    score += 0 if drawdown.startswith("s") else 1 if drawdown.startswith("h") else 2

    style = input("Preference (Steady income / Balanced / Maximum growth): ").lower()
    score += 0 if style.startswith("steady") else 1 if style.startswith("balanced") else 2

    return "Low" if score <= 2 else "Medium" if score <= 4 else "High"

# ---------------------------------------------------------------------------
# Enhanced Market Context & Intelligence Functions
# ---------------------------------------------------------------------------

def get_market_context() -> Dict[str, Any]:
    """Gather comprehensive market context for intelligent analysis."""
    context = {}

    try:
        # Get major market indicators
        for indicator in MARKET_INDICATORS:
            ticker_data = fetch_quote(indicator)
            hist = ticker_data.history("1mo")
            if not hist.empty:
                current = hist["Close"].iloc[-1]
                month_ago = hist["Close"].iloc[0]
                change_pct = ((current - month_ago) / month_ago) * 100
                context[indicator] = {
                    "current": current,
                    "change_1m": change_pct,
                    "trend": "bullish" if change_pct > 2 else "bearish" if change_pct < -2 else "neutral"
                }

        # Determine overall market regime
        sp500_trend = context.get("^GSPC", {}).get("trend", "neutral")
        vix_level = context.get("^VIX", {}).get("current", 20)

        if vix_level > 30:
            regime = "high_volatility"
        elif vix_level < 15 and sp500_trend == "bullish":
            regime = "low_volatility_bull"
        elif sp500_trend == "bearish":
            regime = "risk_off"
        else:
            regime = "neutral"

        context["market_regime"] = regime

    except Exception as e:
        log.warning(f"Error gathering market context: {e}")
        context["market_regime"] = "unknown"

    return context


def analyze_sector_dynamics(ticker: str) -> Dict[str, Any]:
    """Analyze sector-specific dynamics and competitive positioning."""
    try:
        info = fetch_quote(ticker).info or {}
        sector = info.get("sector", "Unknown")
        industry = info.get("industry", "Unknown")

        # Get sector performance context
        sector_context = {
            "sector": sector,
            "industry": industry,
            "market_cap": info.get("marketCap"),
            "employees": info.get("fullTimeEmployees"),
            "business_summary": info.get("longBusinessSummary", "")[:500]
        }

        return sector_context

    except Exception as e:
        log.warning(f"Error analyzing sector dynamics for {ticker}: {e}")
        return {"sector": "Unknown", "industry": "Unknown"}


def enhanced_news_analysis(ticker: str, max_items: int = 10) -> Dict[str, Any]:
    """Enhanced news analysis with LLM-powered insights."""
    try:
        raw_news = (fetch_quote(ticker).news or [])[:max_items]
        # Handle missing 'title' key in news items
        headlines = []
        for n in raw_news:
            if isinstance(n, dict):
                title = n.get("title") or n.get("headline") or n.get("summary", "")
                if title:
                    headlines.append(title)

        if not headlines:
            return {"headlines": [], "sentiment": 0.0, "key_themes": [], "risk_factors": []}

        # Basic sentiment (keeping existing functionality)
        avg_sentiment = 0.0
        if _VADER and headlines:
            scores = [_VADER.polarity_scores(h)["compound"] for h in headlines]
            avg_sentiment = sum(scores) / len(scores)

        # LLM-powered news analysis
        news_text = "\n".join(headlines)
        analysis_prompt = f"""
        Analyze these recent news headlines for {ticker}:

        {news_text}

        Provide analysis in JSON format:
        {{
            "key_themes": ["theme1", "theme2", "theme3"],
            "risk_factors": ["risk1", "risk2"],
            "opportunities": ["opp1", "opp2"],
            "sentiment_explanation": "brief explanation of sentiment",
            "investment_impact": "positive/negative/neutral",
            "urgency": "high/medium/low"
        }}
        """

        llm_analysis = gemini_ask(analysis_prompt)

        try:
            parsed_analysis = json.loads(llm_analysis)
        except:
            parsed_analysis = {
                "key_themes": ["Analysis unavailable"],
                "risk_factors": [],
                "opportunities": [],
                "sentiment_explanation": "LLM analysis failed",
                "investment_impact": "neutral",
                "urgency": "low"
            }

        return {
            "headlines": headlines,
            "sentiment": avg_sentiment,
            **parsed_analysis
        }

    except Exception as e:
        log.warning(f"Error in enhanced news analysis for {ticker}: {e}")
        return {"headlines": [], "sentiment": 0.0, "key_themes": [], "risk_factors": []}


# ---------------------------------------------------------------------------
# Traditional Market‑data helpers (maintained for compatibility)
# ---------------------------------------------------------------------------

def fetch_quote(ticker: str) -> yf.Ticker:
    return yf.Ticker(ticker)


def moving_avgs(series: pd.Series) -> Dict[str, float]:
    return {f"ma{w}": series.rolling(w).mean().iloc[-1] for w in (20, 50, 200)}


def technical_snapshot(ticker: str) -> Dict[str, Any]:
    hist = fetch_quote(ticker).history("2y")
    if hist.empty:
        raise ValueError(f"No price data for {ticker}")
    close = hist["Close"]
    return {
        "current_price": close.iloc[-1],
        **moving_avgs(close),
    }


def fundamental_snapshot(ticker: str) -> Dict[str, Any]:
    info = fetch_quote(ticker).info or {}
    return {
        "pe": info.get("trailingPE"),
        "pb": info.get("priceToBook"),
        "div_yield": info.get("dividendYield"),
    }


def earnings_snapshot(ticker: str) -> Dict[str, Any]:
    q = fetch_quote(ticker)
    cal = q.calendar.T.to_dict() if not q.calendar.empty else {}
    eps = q.earnings.iloc[-1].to_dict() if not q.earnings.empty else {}
    return {"calendar": cal, "last_annual_earnings": eps}


def news_and_sentiment(ticker: str, max_items: int = 5) -> Dict[str, Any]:
    try:
        raw = (fetch_quote(ticker).news or [])[:max_items]
        # Handle missing 'title' key in news items
        heads = []
        for n in raw:
            if isinstance(n, dict):
                title = n.get("title") or n.get("headline") or n.get("summary", "")
                if title:
                    heads.append(title)

        if _VADER and heads:
            scores = [_VADER.polarity_scores(h)["compound"] for h in heads]
            avg = sum(scores) / len(scores)
        else:
            avg = 0.0
        return {"headlines": heads, "avg_sentiment": avg}
    except Exception as e:
        log.warning(f"Error in news analysis for {ticker}: {e}")
        return {"headlines": [], "avg_sentiment": 0.0}

#
# ---------------------------------------------------------------------------
# Enhanced LLM-Powered Analysis Framework
# ---------------------------------------------------------------------------

def comprehensive_stock_analysis(ticker: str, market_context: Dict[str, Any] = None) -> str:
    """
    Comprehensive multi-step LLM analysis using investment frameworks.
    This replaces the simple 180-word analysis with sophisticated reasoning.
    """
    if market_context is None:
        market_context = get_market_context()

    # Gather all available data
    try:
        tech_data = technical_snapshot(ticker)
        fundamental_data = fundamental_snapshot(ticker)
        earnings_data = earnings_snapshot(ticker)
        news_data = enhanced_news_analysis(ticker)
        sector_data = analyze_sector_dynamics(ticker)
    except Exception as e:
        return f"Error gathering data for {ticker}: {e}"

    # Create comprehensive analysis prompt
    analysis_prompt = f"""
    You are a senior equity research analyst with CFA designation. Perform a comprehensive
    analysis of {ticker} using the following structured framework:

    ## MARKET CONTEXT
    Current market regime: {market_context.get('market_regime', 'unknown')}
    Market indicators: {json.dumps(market_context, indent=2)}

    ## COMPANY DATA
    Technical: {json.dumps(tech_data, indent=2)}
    Fundamentals: {json.dumps(fundamental_data, indent=2)}
    Earnings: {json.dumps(earnings_data, indent=2)}
    Sector: {json.dumps(sector_data, indent=2)}
    News Analysis: {json.dumps(news_data, indent=2)}

    ## ANALYSIS FRAMEWORK

    ### 1. BUSINESS QUALITY ASSESSMENT
    - Evaluate competitive advantages and economic moats
    - Assess management quality and capital allocation track record
    - Analyze industry dynamics and competitive positioning
    - Review revenue sustainability and growth drivers
    - Examine margin trends and operational efficiency

    ### 2. FINANCIAL HEALTH EVALUATION
    - Analyze balance sheet strength and debt levels
    - Evaluate cash flow generation and quality
    - Assess profitability trends and return metrics
    - Review working capital management
    - Examine financial flexibility and liquidity

    ### 3. VALUATION ANALYSIS
    - Apply multiple valuation approaches (DCF, multiples, asset-based)
    - Determine fair value estimate with confidence intervals
    - Identify key valuation drivers and sensitivities
    - Compare to historical and peer valuations
    - Assess risk-adjusted return potential

    ### 4. RISK ASSESSMENT
    - Identify company-specific risks and mitigation strategies
    - Evaluate industry and regulatory risks
    - Consider macroeconomic sensitivities
    - Analyze downside scenarios and stress tests
    - Assess ESG and sustainability risks

    ### 5. INVESTMENT THESIS
    - Articulate key investment catalysts
    - Define timeline for thesis to materialize
    - Estimate probability-weighted outcomes
    - Recommend position sizing based on conviction
    - Suggest monitoring metrics and exit criteria

    ## OUTPUT REQUIREMENTS
    Provide a detailed analysis (800-1200 words) covering all framework elements.

    Conclude with:
    - RECOMMENDATION: BUY/HOLD/SELL
    - TARGET PRICE: $X.XX (12-month target)
    - CONVICTION LEVEL: High/Medium/Low
    - POSITION SIZE: X% of portfolio (for diversified investor)
    - KEY RISKS: Top 3 risks to thesis
    - CATALYSTS: Top 3 potential catalysts
    """

    return gemini_ask(analysis_prompt)


def intelligent_stock_screening(
    risk_profile: str,
    market_context: Dict[str, Any] = None,
    investment_themes: List[str] = None,
    tickers: Optional[List[str]] = None
) -> Dict[str, Any]:
    """
    LLM-powered intelligent screening that considers qualitative factors.
    """
    if market_context is None:
        market_context = get_market_context()

    if tickers is None:
        tickers = DEFAULT_TICKERS

    # First, get quantitative screening results (existing logic)
    quantitative_results = screen_stocks(risk_profile, tickers)

    # Enhance with LLM-powered qualitative analysis
    screening_prompt = f"""
    You are a portfolio manager conducting intelligent stock screening.

    ## INVESTMENT CONTEXT
    Risk Profile: {risk_profile}
    Market Context: {json.dumps(market_context, indent=2)}
    Investment Themes: {investment_themes or ["Quality growth", "Value opportunities", "Defensive income"]}

    ## QUANTITATIVE SCREENING RESULTS
    {json.dumps(quantitative_results, indent=2)}

    ## ENHANCED SCREENING TASK

    For each stock in the quantitative results, provide qualitative enhancement:

    1. **Business Quality Score (1-10)**: Assess competitive moats, management, industry position
    2. **Market Opportunity Score (1-10)**: Evaluate growth potential, market dynamics, disruption risk
    3. **Execution Risk Score (1-10)**: Management track record, operational complexity, execution challenges
    4. **Valuation Attractiveness (1-10)**: Current valuation vs intrinsic value and growth prospects
    5. **Thematic Alignment Score (1-10)**: How well does it align with current investment themes

    ## ADDITIONAL SCREENING CRITERIA

    Consider current market regime ({market_context.get('market_regime', 'unknown')}) and recommend:
    - Stocks that benefit from current market conditions
    - Defensive positions if market stress is detected
    - Growth opportunities if market is supportive
    - Value plays if market is oversold

    ## OUTPUT FORMAT

    Provide JSON response:
    {{
        "enhanced_rankings": [
            {{
                "ticker": "AAPL",
                "quantitative_score": 2.5,
                "business_quality": 9,
                "market_opportunity": 8,
                "execution_risk": 3,
                "valuation_attractiveness": 6,
                "thematic_alignment": 8,
                "composite_score": 7.2,
                "rationale": "Brief explanation",
                "recommendation": "BUY/HOLD/SELL"
            }}
        ],
        "market_strategy": "Current market strategy recommendation",
        "top_themes": ["Theme 1", "Theme 2", "Theme 3"],
        "risk_considerations": ["Risk 1", "Risk 2"]
    }}
    """

    try:
        llm_response = gemini_ask(screening_prompt)
        enhanced_results = json.loads(llm_response)
        return enhanced_results
    except Exception as e:
        log.warning(f"LLM screening enhancement failed: {e}")
        # Fallback to quantitative results
        return {
            "enhanced_rankings": [
                {
                    "ticker": ticker,
                    "quantitative_score": score,
                    "composite_score": score,
                    "recommendation": "HOLD",
                    "rationale": "Quantitative analysis only"
                }
                for ticker, score in quantitative_results
            ],
            "market_strategy": "Standard diversified approach",
            "top_themes": ["Quality", "Value", "Growth"],
            "risk_considerations": ["Market volatility", "Economic uncertainty"]
        }


# ---------------------------------------------------------------------------
# Traditional Screening logic (maintained for compatibility)
# ---------------------------------------------------------------------------

def screen_stocks(risk: str, tickers: Optional[List[str]] = None) -> List[Tuple[str, float]]:
    tickers = tickers or DEFAULT_TICKERS
    ranking: List[Tuple[str, float]] = []
    for t in tickers:
        try:
            tech, funda = technical_snapshot(t), fundamental_snapshot(t)
        except Exception as err:  # network issues
            log.debug("Skipping %s: %s", t, err)
            continue
        score = 0.0
        score += tech["current_price"] > tech["ma200"]
        score += funda["pe"] and funda["pe"] < 30
        if risk == "Low" and funda["div_yield"]:
            score += 0.5
        ranking.append((t, score))
    return sorted(ranking, key=lambda x: x[1], reverse=True)[:5]

# ---------------------------------------------------------------------------
# Enhanced Portfolio Intelligence & Investment Planning
# ---------------------------------------------------------------------------

@dataclass
class EnhancedPlan:
    """Enhanced investment plan with LLM-powered insights."""
    ticker: str
    analysis_summary: str
    recommendation: str  # BUY/HOLD/SELL
    target_price: float
    conviction_level: str  # High/Medium/Low

    # Position sizing
    initial_position_pct: float
    max_position_pct: float

    # Entry strategy
    immediate_buy_pct: float
    dca_levels: List[Tuple[float, float]]  # (price, allocation_pct)

    # Risk management
    stop_loss_pct: float
    take_profit_levels: List[Tuple[float, float]]  # (price, sell_pct)

    # Monitoring
    key_catalysts: List[str]
    risk_factors: List[str]
    review_timeline: str

    def pretty_print(self) -> str:
        """Format the enhanced plan for display."""
        return f"""
=== ENHANCED INVESTMENT PLAN: {self.ticker} ===

RECOMMENDATION: {self.recommendation}
Target Price: ${self.target_price:.2f}
Conviction: {self.conviction_level}

POSITION SIZING:
- Initial Position: {self.initial_position_pct:.1f}% of portfolio
- Maximum Position: {self.max_position_pct:.1f}% of portfolio

ENTRY STRATEGY:
- Immediate Buy: {self.immediate_buy_pct:.1f}% of planned position
- DCA Levels: {', '.join([f'${price:.2f} ({pct:.1f}%)' for price, pct in self.dca_levels])}

RISK MANAGEMENT:
- Stop Loss: {self.stop_loss_pct:.1f}% below entry
- Take Profit: {', '.join([f'${price:.2f} ({pct:.1f}% sell)' for price, pct in self.take_profit_levels])}

KEY CATALYSTS:
{chr(10).join([f'- {catalyst}' for catalyst in self.key_catalysts])}

RISK FACTORS:
{chr(10).join([f'- {risk}' for risk in self.risk_factors])}

REVIEW TIMELINE: {self.review_timeline}

ANALYSIS SUMMARY:
{self.analysis_summary}
        """


def create_enhanced_investment_plan(
    ticker: str,
    portfolio_value: float,
    risk_profile: str,
    existing_holdings: Dict[str, float] = None,
    market_context: Dict[str, Any] = None
) -> EnhancedPlan:
    """
    Create an intelligent investment plan using LLM analysis.
    """
    if market_context is None:
        market_context = get_market_context()

    if existing_holdings is None:
        existing_holdings = {}

    # Get comprehensive analysis
    analysis = comprehensive_stock_analysis(ticker, market_context)

    # Extract key information from analysis using LLM
    plan_prompt = f"""
    Based on this comprehensive stock analysis, create a detailed investment plan:

    {analysis}

    ## PORTFOLIO CONTEXT
    Total Portfolio Value: ${portfolio_value:,.2f}
    Risk Profile: {risk_profile}
    Existing Holdings: {json.dumps(existing_holdings, indent=2)}
    Market Context: {json.dumps(market_context, indent=2)}

    ## PLAN REQUIREMENTS

    Create an investment plan considering:
    1. Portfolio diversification and correlation
    2. Risk-adjusted position sizing
    3. Market timing and entry strategy
    4. Risk management and exit strategy
    5. Monitoring and review framework

    ## OUTPUT FORMAT (JSON)
    {{
        "recommendation": "BUY/HOLD/SELL",
        "target_price": 150.00,
        "conviction_level": "High/Medium/Low",
        "initial_position_pct": 5.0,
        "max_position_pct": 8.0,
        "immediate_buy_pct": 60.0,
        "dca_levels": [[145.00, 25.0], [140.00, 15.0]],
        "stop_loss_pct": 15.0,
        "take_profit_levels": [[180.00, 30.0], [200.00, 50.0]],
        "key_catalysts": ["Catalyst 1", "Catalyst 2", "Catalyst 3"],
        "risk_factors": ["Risk 1", "Risk 2", "Risk 3"],
        "review_timeline": "Quarterly review with earnings",
        "analysis_summary": "Brief 2-3 sentence summary of investment thesis"
    }}
    """

    try:
        plan_response = gemini_ask(plan_prompt)
        plan_data = json.loads(plan_response)

        return EnhancedPlan(
            ticker=ticker,
            analysis_summary=plan_data.get("analysis_summary", "Analysis summary unavailable"),
            recommendation=plan_data.get("recommendation", "HOLD"),
            target_price=plan_data.get("target_price", 0.0),
            conviction_level=plan_data.get("conviction_level", "Medium"),
            initial_position_pct=plan_data.get("initial_position_pct", 3.0),
            max_position_pct=plan_data.get("max_position_pct", 5.0),
            immediate_buy_pct=plan_data.get("immediate_buy_pct", 50.0),
            dca_levels=plan_data.get("dca_levels", []),
            stop_loss_pct=plan_data.get("stop_loss_pct", 20.0),
            take_profit_levels=plan_data.get("take_profit_levels", []),
            key_catalysts=plan_data.get("key_catalysts", []),
            risk_factors=plan_data.get("risk_factors", []),
            review_timeline=plan_data.get("review_timeline", "Quarterly")
        )

    except Exception as e:
        log.warning(f"Enhanced plan creation failed for {ticker}: {e}")
        # Fallback to basic plan data
        current_price = technical_snapshot(ticker)["current_price"]

        return EnhancedPlan(
            ticker=ticker,
            analysis_summary="Basic plan due to LLM analysis failure",
            recommendation="HOLD",
            target_price=current_price * 1.1,
            conviction_level="Low",
            initial_position_pct=3.0,
            max_position_pct=5.0,
            immediate_buy_pct=60.0,
            dca_levels=[(current_price * 0.95, 25.0), (current_price * 0.90, 15.0)],
            stop_loss_pct=15.0,
            take_profit_levels=[(current_price * 1.2, 50.0)],
            key_catalysts=["Market recovery", "Earnings growth"],
            risk_factors=["Market volatility", "Economic uncertainty"],
            review_timeline="Quarterly"
        )


def portfolio_correlation_analysis(holdings: Dict[str, float], new_ticker: str) -> Dict[str, Any]:
    """
    Analyze portfolio correlation and diversification impact of adding new position.
    """
    if not holdings:
        return {
            "correlation_risk": "Low",
            "diversification_benefit": "High",
            "recommendation": "Proceed with standard position sizing"
        }

    try:
        # Get correlation data for existing holdings + new ticker
        all_tickers = list(holdings.keys()) + [new_ticker]
        correlation_data = {}

        for ticker in all_tickers:
            try:
                hist = fetch_quote(ticker).history("1y")["Close"]
                if len(hist) > 50:  # Ensure sufficient data
                    correlation_data[ticker] = hist
            except:
                continue

        if len(correlation_data) < 2:
            return {
                "correlation_risk": "Unknown",
                "diversification_benefit": "Unknown",
                "recommendation": "Insufficient data for correlation analysis"
            }

        # Create correlation matrix
        df = pd.DataFrame(correlation_data)
        correlation_matrix = df.corr()

        # Analyze correlation with existing holdings
        if new_ticker in correlation_matrix.columns:
            correlations = correlation_matrix[new_ticker].drop(new_ticker)
            avg_correlation = correlations.mean()
            max_correlation = correlations.max()

            # Determine risk level
            if max_correlation > 0.7:
                risk_level = "High"
                benefit = "Low"
                recommendation = "Consider reducing position size due to high correlation"
            elif avg_correlation > 0.5:
                risk_level = "Medium"
                benefit = "Medium"
                recommendation = "Moderate position sizing recommended"
            else:
                risk_level = "Low"
                benefit = "High"
                recommendation = "Good diversification benefit, standard sizing appropriate"
        else:
            risk_level = "Unknown"
            benefit = "Unknown"
            recommendation = "Correlation analysis failed"

        return {
            "correlation_risk": risk_level,
            "diversification_benefit": benefit,
            "recommendation": recommendation,
            "avg_correlation": avg_correlation if 'avg_correlation' in locals() else None,
            "max_correlation": max_correlation if 'max_correlation' in locals() else None,
            "correlation_details": correlations.to_dict() if 'correlations' in locals() else {}
        }

    except Exception as e:
        log.warning(f"Portfolio correlation analysis failed: {e}")
        return {
            "correlation_risk": "Unknown",
            "diversification_benefit": "Unknown",
            "recommendation": "Analysis failed, use conservative position sizing"
        }


# ---------------------------------------------------------------------------
# Traditional investment‑plan engine (maintained for compatibility)
# ---------------------------------------------------------------------------
@dataclass
class Plan:
    now_qty: int
    dca_price: float
    dca_qty: int
    stop_price: float

    def pretty(self, ticker: str, price: float) -> str:
        dip_pct  = (price - self.dca_price)  / price * 100
        stop_pct = (price - self.stop_price) / price * 100
        return (
            f"Buy {self.now_qty} shares of {ticker} now.\n"
            f"If price dips to €{self.dca_price:.2f} (≈-{dip_pct:.1f}%), buy {self.dca_qty} more.\n"
            f"Set stop‑loss at €{self.stop_price:.2f} (≈-{stop_pct:.1f}%)."
        )


def _auto_levels(price: float, ma50: float | None) -> Tuple[float, float]:
    """Choose DCA & stop if user didn’t supply them."""
    if ma50 and 0.03 < (price - ma50) / price < 0.12:  # 3–12 % below market
        dca = ma50
    else:
        dca = price * 0.90  # 10 % dip
    stop = dca * 0.95       # 5 % below DCA
    return dca, stop


def draft_plan(
    ticker: str,
    cash: float,
    *,
    upfront_fraction: float = 0.6,
    dip_pct: float | None = None,
    stop_pct: float | None = None,
) -> Plan:
    tech = technical_snapshot(ticker)
    price_now = tech["current_price"]

    if dip_pct is None or stop_pct is None:
        dca, stop = _auto_levels(price_now, tech.get("ma50"))
    else:
        dca   = price_now * (1 - dip_pct)
        stop  = price_now * (1 - stop_pct)

    now_qty = max(int((cash * upfront_fraction) // price_now), 1)
    dca_qty = max(int(((cash * (1 - upfront_fraction)) // dca)), 1)
    return Plan(now_qty, dca, dca_qty, stop)

# ---------------------------------------------------------------------------
# Knowledge‑base wrapper (LlamaIndex + Qdrant)
# ---------------------------------------------------------------------------
class KnowledgeBase:
    def __init__(self):
        self.enabled = VectorStoreIndex is not None
        if not self.enabled:
            return
        if os.getenv("QDRANT_URL"):
            store = QdrantVectorStore("stocks_kb", url=os.getenv("QDRANT_URL"))
            ctx   = StorageContext.from_defaults(vector_store=store)
            self.index = VectorStoreIndex([], storage_context=ctx)
        else:
            KB_PATH.mkdir(exist_ok=True)
            self.index = VectorStoreIndex.from_documents([], persist_dir=str(KB_PATH))

    def ingest(self, path: str) -> None:
        if not self.enabled:
            print("[Knowledge base disabled]")
            return
        docs = SimpleDirectoryReader(input_files=[path]).load_data()
        self.index.insert_documents(docs)
        self.index.storage_context.persist()
        print(f"Ingested {len(docs)} document(s)")

    def query(self, q: str) -> str:
        if not self.enabled:
            return "[KB disabled]"
        return str(self.index.query(q))

KB = KnowledgeBase()

# ---------------------------------------------------------------------------
# Agno agent & deterministic workflow
# ---------------------------------------------------------------------------

def build_agent() -> Optional[Agent]:
    if Agent is None:
        return None
    model   = GeminiChat(temperature=0.2)
    memory  = QdrantMemory("stock_mem") if os.getenv("QDRANT_URL") else FileMemory(MEM_PATH)
    agent   = Agent("StockAnalystPro_Enhanced", model, memory, markdown=True)

    # Register enhanced LLM-powered tools
    agent.tool(comprehensive_stock_analysis, name="comprehensive_analysis")
    agent.tool(intelligent_stock_screening, name="intelligent_screening")
    agent.tool(create_enhanced_investment_plan, name="enhanced_investment_plan")
    agent.tool(portfolio_correlation_analysis, name="portfolio_correlation")
    agent.tool(get_market_context, name="market_context")
    agent.tool(analyze_sector_dynamics, name="sector_analysis")
    agent.tool(enhanced_news_analysis, name="enhanced_news")

    # Register traditional tools (for compatibility)
    agent.tool(screen_stocks, name="basic_screening")
    agent.tool(technical_snapshot)
    agent.tool(fundamental_snapshot)
    agent.tool(earnings_snapshot)
    agent.tool(news_and_sentiment, name="basic_news")
    agent.tool(draft_plan, name="basic_investment_plan")
    agent.tool(KB.query, name="knowledge_query")
    agent.tool(gemini_ask, name="ask_gemini")

    return agent


# ---------------------------------------------------------------------------
# Deterministic workflow (Agno TaskGraph)
# ---------------------------------------------------------------------------
if TaskGraph:

    class AnalyseTickerWorkflow(TaskGraph):
        """Enhanced workflow: Market context → Comprehensive analysis → Investment recommendation."""

        ticker: str

        @task
        def gather_market_context(self):
            """Gather market context and indicators."""
            return get_market_context()

        @task
        def comprehensive_analysis(self, market_context):
            """Perform comprehensive LLM-powered analysis."""
            return comprehensive_stock_analysis(self.ticker, market_context)

        @task
        def output(self, analysis):
            return analysis

    class EnhancedScreeningWorkflow(TaskGraph):
        """Enhanced screening workflow with LLM intelligence."""

        risk_profile: str
        tickers: Optional[List[str]] = None

        @task
        def gather_context(self):
            """Gather market context for intelligent screening."""
            return get_market_context()

        @task
        def intelligent_screening(self, market_context):
            """Perform LLM-enhanced screening."""
            return intelligent_stock_screening(
                self.risk_profile,
                market_context,
                tickers=self.tickers
            )

        @task
        def output(self, screening_results):
            return screening_results

    class PortfolioOptimizationWorkflow(TaskGraph):
        """Portfolio-level analysis and optimization workflow."""

        ticker: str
        portfolio_value: float
        risk_profile: str
        existing_holdings: Optional[Dict[str, float]] = None

        @task
        def analyze_correlations(self):
            """Analyze portfolio correlations."""
            holdings = self.existing_holdings or {}
            return portfolio_correlation_analysis(holdings, self.ticker)

        @task
        def create_enhanced_plan(self, correlation_analysis):
            """Create enhanced investment plan considering correlation analysis."""
            # Note: correlation_analysis could be used to adjust position sizing
            # For now, we pass it through the existing holdings
            return create_enhanced_investment_plan(
                self.ticker,
                self.portfolio_value,
                self.risk_profile,
                self.existing_holdings
            )

        @task
        def output(self, enhanced_plan, correlation_analysis):
            return {
                "investment_plan": enhanced_plan,
                "correlation_analysis": correlation_analysis
            }

    # Legacy workflow (maintained for compatibility)
    class LegacyAnalyseTickerWorkflow(TaskGraph):
        """Original simple analysis workflow."""

        ticker: str

        @task
        def gather(self):
            return {
                **technical_snapshot(self.ticker),
                **fundamental_snapshot(self.ticker),
                "earnings": earnings_snapshot(self.ticker),
                "news": news_and_sentiment(self.ticker),
            }

        @task
        def llm(self, data):
            prompt = (
                f"You are a CFA. Analyse {self.ticker} using the JSON below "
                f"and write a concise report (≈180 words). Finish with BUY, "
                f"HOLD or SELL on its own line.\n\n{json.dumps(data)}"
            )
            return gemini_ask(prompt)

        @task
        def output(self, report):
            return report

else:
    AnalyseTickerWorkflow = None  # type: ignore
    EnhancedScreeningWorkflow = None  # type: ignore
    PortfolioOptimizationWorkflow = None  # type: ignore
    LegacyAnalyseTickerWorkflow = None  # type: ignore


# ---------------------------------------------------------------------------
# CLI sub‑command handlers
# ---------------------------------------------------------------------------
def cmd_setup(_: argparse.Namespace) -> None:
    level = risk_quiz()
    RiskProfile(level).save()


def cmd_discover(_: argparse.Namespace) -> None:
    prof = RiskProfile.load()
    picks = screen_stocks(prof.level)
    print(f"Top ideas for {prof.level} risk:")
    for t, s in picks:
        print(f"  {t}: score {s:.2f}")

    if (ag := build_agent()):
        ag.remember("discover", picks)

    print("\nGemini says:\n", gemini_ask(f"Explain why {picks} suit a {prof.level} risk investor."))


def cmd_enhanced_discover(args: argparse.Namespace) -> None:
    """Enhanced discovery with LLM-powered intelligent screening."""
    prof = RiskProfile.load()

    print("🔍 Gathering market context...")
    market_context = get_market_context()
    print(f"Market regime: {market_context.get('market_regime', 'unknown')}")

    print("\n🧠 Running intelligent screening...")
    if EnhancedScreeningWorkflow:
        results = EnhancedScreeningWorkflow(
            risk_profile=prof.level,
            tickers=getattr(args, 'tickers', None)
        ).run()
    else:
        results = intelligent_stock_screening(prof.level, market_context)

    print(f"\n📊 Enhanced screening results for {prof.level} risk profile:")
    print(f"Market Strategy: {results.get('market_strategy', 'N/A')}")
    print(f"Top Themes: {', '.join(results.get('top_themes', []))}")

    print("\n🎯 Top Recommendations:")
    for stock in results.get('enhanced_rankings', [])[:5]:
        print(f"  {stock['ticker']}: {stock['recommendation']} "
              f"(Score: {stock.get('composite_score', 0):.1f}) - {stock.get('rationale', 'N/A')}")

    print(f"\n⚠️  Risk Considerations: {', '.join(results.get('risk_considerations', []))}")

    if (ag := build_agent()):
        ag.remember("enhanced_discover", results)


def cmd_analyse(args: argparse.Namespace) -> None:
    tic = args.ticker.upper()

    # Use enhanced analysis by default, fallback to legacy if requested
    use_legacy = getattr(args, 'legacy', False)

    if use_legacy and LegacyAnalyseTickerWorkflow:
        print("📊 Running legacy analysis...")
        print(LegacyAnalyseTickerWorkflow(ticker=tic).run())
    elif AnalyseTickerWorkflow:
        print("🧠 Running comprehensive LLM analysis...")
        print(AnalyseTickerWorkflow(ticker=tic).run())
    else:
        print("📊 Running basic data analysis...")
        data = {
            **technical_snapshot(tic),
            **fundamental_snapshot(tic),
            "earnings": earnings_snapshot(tic),
            "news": news_and_sentiment(tic),
        }
        print(json.dumps(data, indent=2))


def cmd_comprehensive_analyse(args: argparse.Namespace) -> None:
    """Comprehensive analysis with full LLM capabilities."""
    tic = args.ticker.upper()

    print(f"🔍 Comprehensive Analysis: {tic}")
    print("=" * 50)

    print("\n📈 Gathering market context...")
    market_context = get_market_context()
    print(f"Market regime: {market_context.get('market_regime', 'unknown')}")

    print(f"\n🧠 Running comprehensive analysis for {tic}...")
    analysis = comprehensive_stock_analysis(tic, market_context)
    print(analysis)

    if (ag := build_agent()):
        ag.remember(f"comprehensive_analysis_{tic}", {
            "analysis": analysis,
            "market_context": market_context,
            "timestamp": pd.Timestamp.now().isoformat()
        })


def cmd_plan(args: argparse.Namespace) -> None:
    tic = args.ticker.upper()

    # Use enhanced planning by default, fallback to basic if requested
    use_basic = getattr(args, 'basic', False)

    if use_basic:
        print("📊 Creating basic investment plan...")
        plan = draft_plan(tic, args.cash)
        price_now = technical_snapshot(tic)["current_price"]
        print(plan.pretty(tic, price_now))
        add_plan(tic, plan.__dict__)
    else:
        print("🧠 Creating enhanced investment plan...")
        prof = RiskProfile.load()
        enhanced_plan = create_enhanced_investment_plan(
            tic,
            args.cash,
            prof.level,
            getattr(args, 'holdings', {})
        )
        print(enhanced_plan.pretty_print())

        # Save to database
        add_plan(tic, {
            "type": "enhanced",
            "recommendation": enhanced_plan.recommendation,
            "target_price": enhanced_plan.target_price,
            "conviction": enhanced_plan.conviction_level,
            "position_pct": enhanced_plan.initial_position_pct,
            "plan_details": enhanced_plan.__dict__
        })

    if (ag := build_agent()):
        ag.remember(f"plan_{tic}_{args.cash}", enhanced_plan.__dict__ if not use_basic else plan.__dict__)


def cmd_portfolio_optimize(args: argparse.Namespace) -> None:
    """Portfolio-level optimization and analysis."""
    tic = args.ticker.upper()
    prof = RiskProfile.load()

    print(f"🎯 Portfolio Optimization: {tic}")
    print("=" * 50)

    # Parse existing holdings if provided
    holdings = {}
    if hasattr(args, 'holdings') and args.holdings:
        try:
            holdings = json.loads(args.holdings)
        except:
            print("⚠️  Invalid holdings format, proceeding without existing holdings")

    if PortfolioOptimizationWorkflow:
        print("\n🔍 Running portfolio optimization workflow...")
        result = PortfolioOptimizationWorkflow(
            ticker=tic,
            portfolio_value=args.portfolio_value,
            risk_profile=prof.level,
            existing_holdings=holdings
        ).run()

        print("\n📊 Correlation Analysis:")
        corr = result["correlation_analysis"]
        print(f"Correlation Risk: {corr.get('correlation_risk', 'Unknown')}")
        print(f"Diversification Benefit: {corr.get('diversification_benefit', 'Unknown')}")
        print(f"Recommendation: {corr.get('recommendation', 'N/A')}")

        print("\n💼 Enhanced Investment Plan:")
        print(result["investment_plan"].pretty_print())

    else:
        print("⚠️  Portfolio optimization requires Agno TaskGraph")
        # Fallback to basic analysis
        corr = portfolio_correlation_analysis(holdings, tic)
        plan = create_enhanced_investment_plan(tic, args.portfolio_value, prof.level, holdings)

        print(f"\nCorrelation Analysis: {corr.get('recommendation', 'N/A')}")
        print(f"\nInvestment Plan:\n{plan.pretty_print()}")

    if (ag := build_agent()):
        ag.remember(f"portfolio_optimize_{tic}", result if 'result' in locals() else {"ticker": tic})


def cmd_ingest(args: argparse.Namespace) -> None:
    KB.ingest(args.path)


def cmd_ask(args: argparse.Namespace) -> None:
    print(KB.query(args.question))


def cmd_enhanced_scan(args: argparse.Namespace) -> None:
    """Run enhanced LLM-first comprehensive stock analysis."""
    import asyncio

    # Handle show_limits flag
    if args.show_limits:
        try:
            from llm import print_rate_limit_status
            print_rate_limit_status()
            return
        except ImportError:
            print("❌ Rate limiting module not available")
            return

    try:
        from enhanced_scan_loop import enhanced_single_scan

        print("🚀 Starting Enhanced LLM-First Stock Analysis")
        print("=" * 50)
        print("🧠 This uses comprehensive fundamental analysis instead of momentum-based scoring")
        print("📊 Each stock gets full CFA-level analysis including:")
        print("   • Financial ratios and health metrics")
        print("   • Technical analysis (support/resistance, SMAs)")
        print("   • Earnings quality and revenue trends")
        print("   • Industry comparison and competitive position")
        print("   • Analyst consensus and target prices")
        print("   • Real-time sentiment analysis")
        print("   • Investment thesis and risk/catalyst analysis")
        print()

        # Show configuration
        print(f"⚙️  Configuration:")
        print(f"   • Max stocks per category: {args.max_stocks}")
        print(f"   • Max concurrent LLM calls: {args.max_concurrent}")
        print(f"   • Categories: {args.categories if args.categories else 'all'}")
        print()

        # Estimate LLM calls needed
        num_categories = len(args.categories) if args.categories else 4
        estimated_calls = num_categories * args.max_stocks
        print(f"📊 Estimated LLM calls needed: ~{estimated_calls}")

        # Check if this exceeds daily limit
        try:
            from llm import get_rate_limit_status
            status = get_rate_limit_status()
            if estimated_calls > status["daily_remaining"]:
                print(f"⚠️  Warning: Need ~{estimated_calls} calls but only {status['daily_remaining']} remaining today")
                print(f"   Consider reducing --max_stocks or analyzing fewer categories")

                response = input("Continue anyway? (y/N): ")
                if response.lower() != 'y':
                    print("Scan cancelled")
                    return
        except ImportError:
            pass

        print()

        asyncio.run(enhanced_single_scan(
            max_stocks_per_category=args.max_stocks,
            max_concurrent=args.max_concurrent,
            categories=args.categories
        ))

    except ImportError as e:
        print("❌ Enhanced analysis modules not found. Please ensure enhanced_analysis.py and enhanced_scan_loop.py are available.")
        print(f"Error: {e}")
    except Exception as e:
        print(f"❌ Enhanced scan failed: {e}")
        logging.error(f"Enhanced scan error: {e}")


def cmd_pure_llm(args: argparse.Namespace) -> None:
    """Handle pure LLM commands."""
    import asyncio

    if args.pure_llm_command == "analyze":
        try:
            from smart_rotation_engine import SmartRotationEngine, run_continuous_analysis
            from scheduler import _tickers

            print("🧠 Pure LLM Fundamental Analysis System")
            print("=" * 50)
            print("🎯 Zero momentum bias - LLM makes ALL investment decisions")
            print("📊 Features:")
            print("   • Technical indicators (RSI, MACD, Bollinger Bands)")
            print("   • Comprehensive fundamental analysis")
            print("   • Smart rotation ensuring all stocks get analyzed")
            print("   • Top performers refresh Mon/Wed/Fri")
            print("   • Rate limit compliant (15 calls/minute)")
            print()

            if args.continuous:
                print("🔄 Starting continuous analysis...")
                universe = _tickers()
                asyncio.run(run_continuous_analysis(
                    universe=universe,
                    top_stocks_per_category=args.top_stocks_per_category,
                    calls_per_cycle=args.max_calls,
                    cycle_interval_minutes=args.cycle_interval
                ))
            else:
                print("🔍 Running single analysis cycle...")
                universe = _tickers()
                engine = SmartRotationEngine(args.top_stocks_per_category)

                async def single_cycle():
                    await engine.initialize_universe(universe)
                    results = await engine.run_analysis_cycle(args.max_calls)

                    print(f"\n✅ Analysis cycle complete!")
                    print(f"📊 Results: {results}")

                    # Show top performers
                    summary = engine.get_top_performers_summary()
                    print(f"\n🎯 Current Top Performers:")
                    for category, performers in summary.items():
                        if performers:
                            print(f"  {category.title()}: {', '.join([p['ticker'] for p in performers[:3]])}")

                asyncio.run(single_cycle())

        except ImportError as e:
            print("❌ Pure LLM modules not found.")
            print(f"Error: {e}")
        except Exception as e:
            print(f"❌ Pure LLM analysis failed: {e}")
            logging.error(f"Pure LLM error: {e}")

    elif args.pure_llm_command == "dashboard":
        print("🚀 Launching Pure LLM Dashboard...")
        import subprocess
        import sys
        subprocess.run([
            sys.executable, "-m", "streamlit", "run",
            "dashboard/pure_llm_app.py",
            "--server.port", "8502"
        ])

    elif args.pure_llm_command == "status":
        try:
            from pure_llm_db import get_analysis_stats, get_config
            from llm import print_rate_limit_status

            print("📊 Pure LLM Analysis Status")
            print("=" * 40)

            # Analysis statistics
            stats = get_analysis_stats()
            if stats:
                avg_score = stats['avg_overall_score'] if stats['avg_overall_score'] else 0
                print(f"📈 Analysis Progress:")
                print(f"  Total stocks: {stats['total_stocks']:,}")
                print(f"  Analyzed: {stats['analyzed_stocks']:,}")
                print(f"  Top performers: {stats['top_performers']:,}")
                print(f"  Average score: {avg_score:.1f}")
                print()
                print(f"🎯 Recommendations:")
                print(f"  BUY: {stats['buy_recommendations']}")
                print(f"  HOLD: {stats['hold_recommendations']}")
                print(f"  SELL: {stats['sell_recommendations']}")
                print()
            else:
                print("📈 No analysis data available yet.")
                print()

            # Configuration
            top_stocks = get_config("top_stocks_per_category", "5")
            print(f"⚙️  Configuration:")
            print(f"  Top stocks per category: {top_stocks}")
            print()

            # Rate limits
            print_rate_limit_status()

        except ImportError as e:
            print("❌ Pure LLM modules not available.")
            print(f"Error: {e}")

    else:
        print("❌ Unknown pure-llm command. Use: analyze, dashboard, or status")


def cmd_serve(_: argparse.Namespace) -> None:
    """Intelligent dashboard launcher - detects which system is in use."""
    import subprocess
    import sys
    from pathlib import Path

    # Check for pure LLM database and data
    pure_llm_db = Path.home() / "pure_llm_stocks.db"
    has_pure_llm_data = False

    if pure_llm_db.exists():
        try:
            import sqlite3
            conn = sqlite3.connect(pure_llm_db)
            cursor = conn.execute("SELECT COUNT(*) FROM pure_llm_stocks WHERE llm_overall_score IS NOT NULL")
            count = cursor.fetchone()[0]
            conn.close()
            has_pure_llm_data = count > 0
        except Exception:
            has_pure_llm_data = False

    if has_pure_llm_data:
        print("🧠 Detected Pure LLM analysis data")
        print("🚀 Launching Pure LLM Dashboard (NO ROI bias)...")
        print("📊 URL: http://localhost:8502")
        print("💡 Features: LLM scores, fundamental analysis, zero momentum bias")
        subprocess.run([
            sys.executable, "-m", "streamlit", "run",
            "dashboard/pure_llm_app.py",
            "--server.port", "8502"
        ])
    else:
        print("📊 No Pure LLM data detected")
        print("🚀 Launching Legacy Dashboard...")
        print("📊 URL: http://localhost:8501")
        print("💡 Features: Traditional momentum + LLM analysis")
        print("🔄 To use Pure LLM: run 'python stock_agent_v2.py pure-llm analyze' first")
        os.execvp("streamlit", ["streamlit", "run", "dashboard/app.py"])


def cmd_chat(_: argparse.Namespace) -> None:
    ag = build_agent()
    if not ag:
        print("Chat mode requires the Agno package.")
        return

    print("Interactive chat – type 'exit' to quit.")
    while True:
        try:
            msg = input("You: ")
        except (EOFError, KeyboardInterrupt):
            print()
            break
        if msg.lower() in {"exit", "quit"}:
            break
        print("Agent:", ag.run(msg))


# ---------------------------------------------------------------------------
# Argument parser & entrypoint
# ---------------------------------------------------------------------------
def build_parser() -> argparse.ArgumentParser:
    p = argparse.ArgumentParser(description="Stock Analyst Pro - Enhanced LLM Edition")
    sub = p.add_subparsers(dest="cmd", required=True)

    # Basic commands
    sub.add_parser("setup", help="Run risk‑tolerance quiz")

    # Discovery commands
    sub.add_parser("discover", help="Basic market screening")
    enh_disc = sub.add_parser("enhanced-discover", help="🧠 Enhanced LLM-powered market discovery")
    enh_disc.add_argument("--tickers", nargs="+", help="Custom ticker list")

    # Analysis commands
    ana = sub.add_parser("analyse", help="Stock analysis (enhanced by default)")
    ana.add_argument("ticker")
    ana.add_argument("--legacy", action="store_true", help="Use legacy 180-word analysis")

    comp_ana = sub.add_parser("comprehensive", help="🧠 Comprehensive LLM analysis")
    comp_ana.add_argument("ticker")

    # Planning commands
    pl = sub.add_parser("plan", help="Investment planning (enhanced by default)")
    pl.add_argument("ticker")
    pl.add_argument("cash", type=float)
    pl.add_argument("--basic", action="store_true", help="Use basic planning")
    pl.add_argument("--holdings", help="JSON string of existing holdings")

    port_opt = sub.add_parser("portfolio", help="🎯 Portfolio optimization & correlation analysis")
    port_opt.add_argument("ticker")
    port_opt.add_argument("portfolio_value", type=float)
    port_opt.add_argument("--holdings", help="JSON string of existing holdings")

    # Knowledge base commands
    ing = sub.add_parser("ingest", help="Add PDF/HTML/CSV to knowledge base")
    ing.add_argument("path")

    ask = sub.add_parser("ask", help="Query the document knowledge base")
    ask.add_argument("question")

    # Interactive commands
    sub.add_parser("chat", help="Interactive chat agent")

    # System commands
    scan_p = sub.add_parser("scan", help="Run continuous market scanner (momentum-based)")
    scan_p.add_argument(
        "--run_at_start",
        action="store_true",
        help="Run one full scan immediately, then continue on the scheduled cycles",
    )

    # Enhanced LLM-first analysis
    enhanced_scan_p = sub.add_parser("enhanced-scan", help="🧠 Run enhanced LLM-first comprehensive analysis")
    enhanced_scan_p.add_argument(
        "--max_stocks",
        type=int,
        default=5,
        help="Maximum stocks to analyze per category (default: 5 for free tier)"
    )
    enhanced_scan_p.add_argument(
        "--max_concurrent",
        type=int,
        default=1,
        help="Maximum concurrent LLM calls (default: 1 for free tier)"
    )
    enhanced_scan_p.add_argument(
        "--categories",
        nargs="+",
        choices=["stable", "emerging", "pharma", "moon"],
        help="Categories to analyze (default: all)"
    )
    enhanced_scan_p.add_argument(
        "--show_limits",
        action="store_true",
        help="Show current rate limits and exit"
    )

    # Pure LLM system
    pure_llm_p = sub.add_parser("pure-llm", help="🧠 Pure LLM fundamental analysis (zero momentum bias)")
    pure_llm_subparsers = pure_llm_p.add_subparsers(dest="pure_llm_command", help="Pure LLM commands")

    # Pure LLM analyze command
    pure_analyze_p = pure_llm_subparsers.add_parser("analyze", help="Run analysis cycle")
    pure_analyze_p.add_argument(
        "--top_stocks_per_category",
        type=int,
        default=5,
        help="Number of top stocks to track per category (default: 5)"
    )
    pure_analyze_p.add_argument(
        "--max_calls",
        type=int,
        default=15,
        help="Maximum LLM calls per cycle (default: 15)"
    )
    pure_analyze_p.add_argument(
        "--continuous",
        action="store_true",
        help="Run continuous analysis"
    )
    pure_analyze_p.add_argument(
        "--cycle_interval",
        type=int,
        default=60,
        help="Minutes between cycles in continuous mode (default: 60)"
    )

    # Pure LLM dashboard command
    pure_dashboard_p = pure_llm_subparsers.add_parser("dashboard", help="Launch pure LLM dashboard")

    # Pure LLM status command
    pure_status_p = pure_llm_subparsers.add_parser("status", help="Show analysis status")

    sub.add_parser("serve", help="🚀 Launch dashboard (auto-detects Pure LLM vs Legacy)")

    return p


def main() -> None:
    args = build_parser().parse_args()

    # Enhanced command routing
    command_map = {
        # Basic commands
        "setup": cmd_setup,

        # Discovery commands
        "discover": cmd_discover,
        "enhanced-discover": cmd_enhanced_discover,

        # Analysis commands
        "analyse": cmd_analyse,
        "comprehensive": cmd_comprehensive_analyse,

        # Planning commands
        "plan": cmd_plan,
        "portfolio": cmd_portfolio_optimize,

        # Knowledge base commands
        "ingest": cmd_ingest,
        "ask": cmd_ask,

        # Interactive commands
        "chat": cmd_chat,

        # System commands
        "scan": lambda a: (
            subprocess.run([sys.executable, "-m", "scan_loop", "run_once"]) if a.run_at_start else None,
            os.execvp(sys.executable, [sys.executable, "-m", "scan_loop", "run"])
        )[-1],
        "enhanced-scan": cmd_enhanced_scan,
        "pure-llm": cmd_pure_llm,
        "serve": cmd_serve,
    }

    if args.cmd in command_map:
        try:
            command_map[args.cmd](args)
        except Exception as e:
            log.error(f"Command '{args.cmd}' failed: {e}")
            print(f"❌ Error: {e}")
            sys.exit(1)
    else:
        print(f"❌ Unknown command: {args.cmd}")
        sys.exit(1)


if __name__ == "__main__":
    # Display enhanced capabilities banner
    print("🚀 Stock Analyst Pro - Enhanced LLM Edition v2.0")
    print("=" * 55)
    print("🧠 New Enhanced Commands:")
    print("  enhanced-discover  - Intelligent market screening")
    print("  comprehensive     - Deep LLM analysis")
    print("  portfolio         - Portfolio optimization")
    print("📊 Traditional commands still available with --legacy/--basic flags")
    print("=" * 55)
    print()

    main()
