# Gemini Free Tier Rate Limiting Guide

## 🚨 Problem Identified

You encountered 429 quota errors because the Gemini **free tier has much stricter limits** than initially configured:

```
'quotaMetric': 'generativelanguage.googleapis.com/generate_content_free_tier_requests'
'quotaId': 'GenerateRequestsPerMinutePerProjectPerModel-FreeTier'
'quotaValue': '15'
```

**Actual Gemini Free Tier Limits:**
- ✅ **Daily**: 1,500 requests per day
- ❌ **Per-minute**: Only **15 requests per minute** (not 60!)
- ❌ **Concurrent**: Free tier doesn't handle high concurrency well

## ✅ Fixed Configuration

### Updated Default Limits
```python
# OLD (too aggressive for free tier)
LLM_RATE_LIMIT_PER_MINUTE = 60    # ❌ Too high
LLM_BATCH_SIZE = 10               # ❌ Too large  
LLM_DELAY_BETWEEN_CALLS = 1.0     # ❌ Too fast
LLM_MAX_CONCURRENT = 3            # ❌ Too many

# NEW (optimized for free tier)
LLM_RATE_LIMIT_PER_MINUTE = 15    # ✅ Matches free tier
LLM_BATCH_SIZE = 5                # ✅ Smaller batches
LLM_DELAY_BETWEEN_CALLS = 4.5     # ✅ 4.5s = ~13 calls/minute (safe)
LLM_MAX_CONCURRENT = 1            # ✅ Sequential processing
```

### Enhanced Error Handling
```python
# Added exponential backoff for 429 errors
if "429" in error_str or "quota" in error_str.lower():
    retry_delay = base_delay * (2 ** attempt)  # 5s, 10s, 20s
    await asyncio.sleep(retry_delay)
```

## 🎯 Recommended Usage for Free Tier

### 1. **Conservative Scan (Recommended)**
```bash
python stock_agent_v2.py enhanced-scan \
  --max_stocks 3 \
  --max_concurrent 1 \
  --categories stable
```
**Result**: 3 calls, ~15 seconds, very safe

### 2. **Moderate Scan**
```bash
python stock_agent_v2.py enhanced-scan \
  --max_stocks 5 \
  --max_concurrent 1 \
  --categories stable emerging
```
**Result**: 10 calls, ~45 seconds, safe

### 3. **Full Scan (Use Carefully)**
```bash
python stock_agent_v2.py enhanced-scan \
  --max_stocks 5 \
  --max_concurrent 1
```
**Result**: 20 calls, ~90 seconds, pushes limits

## ⚙️ Free Tier Optimization

### Environment Variables for Free Tier
```bash
# Set ultra-conservative limits
export LLM_RATE_LIMIT_PER_MINUTE=12    # Even more conservative
export LLM_DELAY_BETWEEN_CALLS=5.5     # Slower pace
export LLM_BATCH_SIZE=3                 # Tiny batches
export LLM_MAX_CONCURRENT=1             # Always sequential
```

### Check Your Limits First
```bash
# Always check before running
python stock_agent_v2.py enhanced-scan --show_limits
```

Expected output:
```
📊 LLM Rate Limit Status:
   Daily: 0/1500 (1500 remaining)
   This minute: 0/15 (15 remaining)    # ← Key limit!
   Batch size: 5
   Delay between calls: 4.5s
```

## 🕐 Timing Strategy

### Optimal Timing
- **4.5 seconds between calls** = ~13 calls per minute
- **Safe margin** below 15/minute limit
- **Automatic retry** with exponential backoff

### Example Timeline
```
Call 1:  00:00 ✅
Call 2:  00:05 ✅ (4.5s delay)
Call 3:  00:09 ✅
Call 4:  00:14 ✅
Call 5:  00:18 ✅
...
Call 13: 00:54 ✅ (13 calls in 54 seconds)
Call 14: 01:03 ✅ (new minute starts)
```

## 🛡️ Error Handling Improvements

### 429 Error Recovery
```python
# Automatic retry with exponential backoff
Attempt 1: Wait 5 seconds
Attempt 2: Wait 10 seconds  
Attempt 3: Wait 20 seconds
Final: Return "[Gemini quota error]"
```

### Better Error Messages
```
⚠️  Rate limit hit for AAPL, retrying in 5s (attempt 1/3)
⚠️  Rate limit hit for AAPL, retrying in 10s (attempt 2/3)
❌ Rate limit exceeded for AAPL after 3 attempts
```

## 📊 Free Tier Usage Patterns

### Pattern 1: Small Frequent Scans
```bash
# Run 3-5 stocks every hour
python stock_agent_v2.py enhanced-scan --max_stocks 3 --categories stable
# Wait 1 hour
python stock_agent_v2.py enhanced-scan --max_stocks 3 --categories emerging
```

### Pattern 2: Daily Comprehensive Scan
```bash
# Once per day, analyze more stocks
python stock_agent_v2.py enhanced-scan --max_stocks 8 --max_concurrent 1
# Uses ~32 calls, well within daily limit
```

### Pattern 3: Category Focus
```bash
# Monday: Stable stocks
python stock_agent_v2.py enhanced-scan --max_stocks 10 --categories stable

# Tuesday: Emerging stocks  
python stock_agent_v2.py enhanced-scan --max_stocks 10 --categories emerging
```

## 🎯 Monitoring Your Usage

### Real-time Monitoring
```bash
# Check quota before each run
python rate_limit_config.py status

# Estimate calls needed
python rate_limit_config.py estimate 2 5
# Output: 10 calls needed, 2.5 minutes estimated
```

### Usage Tracking
The system now tracks:
- ✅ Calls made this minute
- ✅ Calls made today
- ✅ Automatic rate limiting
- ✅ Retry attempts with backoff

## ✅ Expected Results

### Before Fix (Failing)
```
Sending 10 stocks to LLM for analysis...
Gemini error: 429 POST... quota exceeded
❌ Multiple failures due to rate limiting
```

### After Fix (Working)
```
🧠 Sending 5 stocks to LLM for analysis...
📊 LLM Quota: 1485/1500 calls remaining today
⏱️  Processing with 4.5s delays (free tier optimized)
✅ Processed 5/5 stocks successfully
📊 LLM calls used: 1490/1500 today
```

## 🚀 Upgrade Path

When you're ready to upgrade from free tier:

### Paid Tier Benefits
- **Higher rate limits**: 1000+ requests per minute
- **Better concurrency**: Multiple simultaneous calls
- **Faster processing**: Shorter delays between calls

### Configuration for Paid Tier
```bash
export LLM_RATE_LIMIT_PER_MINUTE=300
export LLM_DELAY_BETWEEN_CALLS=0.5
export LLM_MAX_CONCURRENT=5
export LLM_BATCH_SIZE=20
```

## 💡 Pro Tips for Free Tier

1. **Start small**: Test with 3 stocks first
2. **Monitor closely**: Check quota before each run
3. **Be patient**: 4.5s delays are necessary
4. **Focus categories**: Don't analyze all categories at once
5. **Time wisely**: Spread usage throughout the day

The system is now optimized for Gemini free tier limits and should work reliably! 🎯
