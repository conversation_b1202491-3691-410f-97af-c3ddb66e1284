# Stock Analyst Pro v2.0 - Quick Reference Card

## 🎯 Three Analysis Systems

| System | Command | Best For |
|--------|---------|----------|
| **🧠 Pure LLM** | `python stock_agent_v2.py pure-llm analyze` | Zero momentum bias - comprehensive fundamental analysis |
| **⚡ Enhanced Scan** | `python stock_agent_v2.py enhanced-scan` | Hybrid momentum + AI validation |
| **📊 Legacy Scan** | `python stock_agent_v2.py scan` | Traditional momentum analysis |

## 🚀 Quick Start (30 seconds)

```bash
# Setup
export GEMINI_API_KEY="your_key_here"
pip install yfinance pandas google-generativeai streamlit numpy

# Pure LLM Analysis (RECOMMENDED)
python stock_agent_v2.py pure-llm analyze --max_calls 15
python stock_agent_v2.py pure-llm dashboard  # Port 8502
```

## 🧠 Pure LLM Commands (Zero Momentum Bias)

### Analysis
```bash
# Single cycle with defaults
python stock_agent_v2.py pure-llm analyze --max_calls 15 --top_stocks_per_category 5

# Continuous analysis
python stock_agent_v2.py pure-llm analyze --continuous --cycle_interval 60

# Conservative (free tier safe)
python stock_agent_v2.py pure-llm analyze --max_calls 10 --top_stocks_per_category 3
```

### Monitoring
```bash
# Check status and quota
python stock_agent_v2.py pure-llm status

# Launch dashboard (dual views)
python stock_agent_v2.py pure-llm dashboard  # http://localhost:8502

# Rate limit utilities
python rate_limit_config.py suggest
python rate_limit_config.py status
```

## 📊 Dashboard Views

- **View 1**: Top N performers per category (configurable)
- **View 2**: All analyzed stocks with filtering
- **View 3**: Analysis progress and statistics

## ⚙️ Key Configuration

```bash
# Rate limiting (free tier optimized)
--max_calls 15                    # LLM calls per cycle
--top_stocks_per_category 5       # Top stocks to track

# Continuous mode
--continuous                      # Run continuously
--cycle_interval 60               # Minutes between cycles
```

## 🎯 What's Different

| Old System | New Pure LLM System |
|------------|---------------------|
| ❌ Momentum-based ranking | ✅ Pure fundamental analysis |
| ❌ Historical bias | ✅ Zero momentum calculations |
| ❌ Simple scoring | ✅ 4-component LLM scoring |
| ❌ Basic dashboard | ✅ Dual-view rich dashboard |

## 🛡️ Free Tier Limits

- **Daily**: 1,500 calls
- **Per-minute**: 15 calls
- **Recommended**: 10-15 calls per cycle
- **Delay**: 4.5 seconds between calls

## 🔄 Legacy Commands (Still Available)

```bash
python stock_agent_v2.py scan &           # Traditional scanner
python stock_agent_v2.py serve            # Legacy dashboard
python stock_agent_v2.py analyse AAPL     # Individual analysis
python stock_agent_v2.py plan AAPL 1000   # Investment planning
```

## 📈 Usage Patterns

### Daily Analysis
```bash
# Morning: Check status
python stock_agent_v2.py pure-llm status

# Run analysis cycle
python stock_agent_v2.py pure-llm analyze --max_calls 15

# View results
python stock_agent_v2.py pure-llm dashboard
```

### Continuous Mode
```bash
# Set and forget
python stock_agent_v2.py pure-llm analyze --continuous --cycle_interval 90
```

## 🎯 Best Practices

1. **Start small**: Use `--max_calls 10` initially
2. **Monitor quota**: Check status regularly
3. **Use suggestions**: Run `rate_limit_config.py suggest`
4. **Focus categories**: Analyze specific categories if needed
5. **Continuous mode**: For hands-off analysis

## 🧠 LLM Analysis Features

- **Technical Indicators**: RSI, MACD, Bollinger Bands, SMAs
- **Fundamental Analysis**: P/E, ROE, debt ratios, earnings
- **4-Component Scoring**: Overall, Growth, Risk, Sentiment (0-100)
- **Smart Rotation**: Ensures all stocks get analyzed
- **Top Performer Tracking**: Mon/Wed/Fri refresh schedule

---

**The LLM now makes ALL investment decisions based on comprehensive fundamental + technical analysis with zero momentum bias!** 🎯🧠📊
