import os
import google.generativeai as genai
from google.api_core import exceptions
from dotenv import load_dotenv

load_dotenv(override=True)

# --- Configuration ---
# Ensure your GOOGLE_API_KEY environment variable is set
# Example (in your terminal before running the script):
# export GOOGLE_API_KEY='YOUR_API_KEY'
# On Windows Command Prompt: set GOOGLE_API_KEY=YOUR_API_KEY
# On Windows PowerShell: $env:GOOGLE_API_KEY="YOUR_API_KEY"
API_KEY = os.environ.get("GOOGLE_API_KEY")

if not API_KEY:
    print("Error: GOOGLE_API_KEY environment variable not set.")
    print("Please set it before running the script.")
    exit() # Exit if API key is not found

# Configure the google-generativeai library
try:
    genai.configure(api_key=API_KEY)
    print("Google Generative AI configured successfully.")
except Exception as e:
    print(f"Error configuring Google Generative AI: {e}")
    print("Please double-check your API key.")
    exit()

# --- Function to List Available Models ---
def list_available_models():
    """Prints a list of generative models available via the API."""
    print("\n--- Available Generative Models ---")
    try:
        # Use genai.list_models() to get the list of models
        for model in genai.list_models():
            # Filter for models that support generating content (text, chat, multimodal)
            if 'generateContent' in model.supported_generation_methods:
                print(f"- {model.name}")
            # You could also list others if interested, e.g., embeddings
            # elif 'embedContent' in model.supported_generation_methods:
            #      print(f"- {model.name} (Embedding)")

    except exceptions.GoogleAPIError as e:
        print(f"Error listing models: Google API Error: {e}")
        print("Ensure your API key is correct and has permissions to access the Generative Language API.")
    except Exception as e:
        print(f"An unexpected error occurred while listing models: {e}")
    print("-----------------------------------\n")

# --- Main Execution ---
if __name__ == "__main__":
    print("Attempting to list available models...")
    list_available_models()
    print("Script finished.")