"""
Enhanced LLM-first scanning system.
Replaces momentum-based scoring with comprehensive fundamental analysis.
"""

from __future__ import annotations
import asyncio
import logging
import time
from pathlib import Path
from typing import List, Dict, Any
import yfinance as yf

from enhanced_analysis import batch_enhanced_analysis, ComprehensiveAnalysis
from scoring import classify
from stock_agent_v2 import gemini_ask
from db import upsert_stock

# Logging setup
LOG_FILE = Path.home() / "enhanced_scan.log"
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s %(levelname)s: %(message)s",
    handlers=[
        logging.FileHandler(LOG_FILE, encoding="utf-8"),
        logging.StreamHandler(),
    ],
)

SUMMARY = Path.home() / ".enhanced_scan_summary.txt"


def store_enhanced_analysis(analysis: ComprehensiveAnalysis) -> None:
    """Store comprehensive analysis in database with enhanced schema."""
    
    record = {
        "ticker": analysis.ticker,
        "category": classify(yf.Ticker(analysis.ticker).info or {}),
        "recommendation": analysis.recommendation,
        "conviction_score": analysis.conviction_score,
        "target_price": analysis.target_price,
        "current_price": analysis.current_price,
        "financial_health_score": analysis.financial_health_score,
        "technical_score": analysis.technical_score,
        "sentiment_score": analysis.sentiment_score,
        "support_level": analysis.support_level,
        "resistance_level": analysis.resistance_level,
        "earnings_quality": analysis.earnings_quality,
        "revenue_trend": analysis.revenue_trend,
        "industry_position": analysis.industry_position,
        "analyst_consensus": analysis.analyst_consensus,
        "investment_thesis": analysis.investment_thesis,
        "key_risks": ", ".join(analysis.key_risks),
        "key_catalysts": ", ".join(analysis.key_catalysts),
        "last_checked": int(time.time()),
        
        # Legacy fields for compatibility
        "score": analysis.conviction_score,  # Map conviction to legacy score
        "price_now": analysis.current_price,
        "roi_window": "LLM",  # Indicate this is LLM-based, not momentum
        "potential_gain": calculate_potential_gain(analysis),
        "llm_opinion": f"{analysis.recommendation} – {analysis.investment_thesis}",
        "risk": map_conviction_to_risk(analysis.conviction_score)
    }
    
    upsert_stock(record)


def calculate_potential_gain(analysis: ComprehensiveAnalysis) -> float:
    """Calculate potential gain percentage from target price."""
    if analysis.target_price and analysis.current_price > 0:
        return ((analysis.target_price / analysis.current_price) - 1) * 100
    return 0.0


def map_conviction_to_risk(conviction: float) -> str:
    """Map conviction score to risk level."""
    if conviction >= 80:
        return "Low"
    elif conviction >= 60:
        return "Medium"
    else:
        return "High"


async def enhanced_category_scan(
    category: str,
    universe: List[str],
    max_stocks: int = 20,
    max_concurrent: int = 3
) -> List[ComprehensiveAnalysis]:
    """Scan a category using comprehensive LLM analysis."""
    
    print(f"\n🎯 Processing category: {category}")
    
    # Filter universe to category
    target_stocks = []
    for ticker in universe[:200]:  # Limit universe scan for performance
        try:
            info = yf.Ticker(ticker).info or {}
            if classify(info) == category:
                target_stocks.append(ticker)
                if len(target_stocks) >= max_stocks:
                    break
        except Exception:
            continue
    
    if not target_stocks:
        print(f"   No {category} stocks found")
        return []
    
    print(f"   Found {len(target_stocks)} {category} stocks: {', '.join(target_stocks)}")
    # Check rate limits before starting
    from llm import get_rate_limit_status, print_rate_limit_status
    status = get_rate_limit_status()

    if status["daily_remaining"] < len(target_stocks):
        print(f"   ⚠️  Warning: Only {status['daily_remaining']} LLM calls remaining today, but need {len(target_stocks)}")
        print(f"   📊 Will analyze first {status['daily_remaining']} stocks only")
        target_stocks = target_stocks[:status['daily_remaining']]

    if not target_stocks:
        print(f"   ❌ No LLM calls available today ({status['calls_today']}/{status['daily_limit']} used)")
        return []

    print(f"   🧠 Performing comprehensive LLM analysis on {len(target_stocks)} stocks...")
    print(f"   ⚙️  Rate limits: {max_concurrent} concurrent, {status['daily_remaining']} calls remaining")

    # Perform comprehensive analysis on all stocks
    analyses = await batch_enhanced_analysis(
        target_stocks,
        gemini_ask,
        max_concurrent=max_concurrent,
        show_progress=True
    )
    
    if not analyses:
        print(f"   ❌ No successful analyses for {category}")
        return []
    
    print(f"   ✅ Successfully analyzed {len(analyses)} stocks")
    
    # Display results
    buy_recommendations = []
    for analysis in analyses:
        target_price_str = f"${analysis.target_price:.2f}" if analysis.target_price else "N/A"
        print(f"     📊 {analysis.ticker}: {analysis.recommendation} "
              f"(Conviction: {analysis.conviction_score:.0f}%, "
              f"Target: {target_price_str}) "
              f"- {analysis.investment_thesis[:60]}...")
        
        # Store in database
        store_enhanced_analysis(analysis)
        
        # Track strong buy recommendations
        if analysis.recommendation == "BUY" and analysis.conviction_score >= 70:
            buy_recommendations.append(analysis.ticker)
            print(f"     🎯 {analysis.ticker} added to strong BUY list!")
    
    result = f"[{category}] Enhanced scan complete. Strong BUYs: {', '.join(buy_recommendations) if buy_recommendations else 'none'}"
    print(f"   {result}")
    logging.info(result)
    
    # Append to summary
    with open(SUMMARY, "a") as f:
        f.write(result + "\n")
    
    return analyses


async def enhanced_scan_all_categories(
    max_stocks_per_category: int = 15,
    max_concurrent: int = 3,
    categories: List[str] = None
) -> Dict[str, List[ComprehensiveAnalysis]]:
    """Run enhanced LLM-first scan across all categories."""

    print("🚀 Enhanced LLM-First Stock Analysis")
    print("=" * 50)

    # Show rate limit status
    from llm import print_rate_limit_status
    print_rate_limit_status()
    print()

    # Get universe
    from scheduler import _tickers
    universe = _tickers()
    print(f"📊 Universe size: {len(universe)}")
    print(f"⚙️  Configuration: {max_stocks_per_category} stocks/category, {max_concurrent} concurrent calls")

    if categories is None:
        categories = ["stable", "emerging", "pharma", "moon"]

    results = {}

    for category in categories:
        try:
            analyses = await enhanced_category_scan(
                category,
                universe,
                max_stocks=max_stocks_per_category,
                max_concurrent=max_concurrent
            )
            results[category] = analyses

            # Brief pause between categories to respect API limits
            await asyncio.sleep(3)

        except Exception as e:
            error_msg = f"[{category}] Enhanced scan failed: {e}"
            print(f"   ❌ {error_msg}")
            logging.error(error_msg)
            results[category] = []
    
    # Summary
    total_analyzed = sum(len(analyses) for analyses in results.values())
    total_buys = sum(
        len([a for a in analyses if a.recommendation == "BUY" and a.conviction_score >= 70])
        for analyses in results.values()
    )
    
    print(f"\n✅ Enhanced scan completed!")
    print(f"📈 Total stocks analyzed: {total_analyzed}")
    print(f"🎯 Strong BUY recommendations: {total_buys}")
    
    return results


async def enhanced_single_scan(
    max_stocks_per_category: int = 15,
    max_concurrent: int = 3,
    categories: List[str] = None
):
    """Run a single enhanced scan cycle and exit."""
    print("🔍 Running enhanced single scan cycle...")

    results = await enhanced_scan_all_categories(
        max_stocks_per_category=max_stocks_per_category,
        max_concurrent=max_concurrent,
        categories=categories
    )
    
    print("\n📊 ENHANCED SCAN SUMMARY")
    print("=" * 40)
    
    for category, analyses in results.items():
        if not analyses:
            continue
            
        print(f"\n{category.upper()} CATEGORY:")
        
        # Show top 3 by conviction
        top_picks = sorted(analyses, key=lambda x: x.conviction_score, reverse=True)[:3]
        
        for i, analysis in enumerate(top_picks, 1):
            gain_pct = calculate_potential_gain(analysis)
            print(f"  {i}. {analysis.ticker} - {analysis.recommendation} "
                  f"({analysis.conviction_score:.0f}% conviction, "
                  f"{gain_pct:+.1f}% potential)")
            print(f"     💡 {analysis.investment_thesis}")
            if analysis.key_catalysts:
                print(f"     🚀 Catalysts: {', '.join(analysis.key_catalysts[:2])}")
            if analysis.key_risks:
                print(f"     ⚠️  Risks: {', '.join(analysis.key_risks[:2])}")
    
    print(f"\n✅ Enhanced single scan cycle completed!")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "run_once":
        asyncio.run(enhanced_single_scan())
    else:
        print("Usage: python enhanced_scan_loop.py run_once")
        print("Note: This is the enhanced LLM-first analysis system")
        print("Use this instead of the momentum-based scan_loop.py")
