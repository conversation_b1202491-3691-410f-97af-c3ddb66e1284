"""
Enhanced LLM-first stock analysis system.
Replaces momentum-based scoring with comprehensive fundamental analysis.
"""

from __future__ import annotations
import asyncio
import json
import yfinance as yf
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from datetime import datetime, timedelta


@dataclass
class ComprehensiveAnalysis:
    """Structured result from comprehensive LLM analysis."""
    ticker: str
    recommendation: str  # BUY/HOLD/SELL
    conviction_score: float  # 0-100
    target_price: Optional[float]
    current_price: float
    
    # Financial Health
    financial_health_score: float  # 0-100
    key_ratios: Dict[str, float]
    
    # Technical Analysis
    technical_score: float  # 0-100
    support_level: Optional[float]
    resistance_level: Optional[float]
    sma_analysis: Dict[str, float]
    
    # Fundamental Analysis
    earnings_quality: str
    revenue_trend: str
    industry_position: str
    
    # Market Sentiment
    sentiment_score: float  # -1 to +1
    analyst_consensus: str
    recent_news_impact: str
    
    # Detailed Reasoning
    investment_thesis: str
    key_risks: List[str]
    key_catalysts: List[str]
    
    # Metadata
    analysis_date: datetime
    data_sources: List[str]


def create_comprehensive_prompt(ticker: str, data: Dict[str, Any]) -> str:
    """Create comprehensive analysis prompt based on user's template."""
    
    return f"""
Given the stock ticker {ticker}, provide a full and up-to-date financial analysis covering the following aspects:

## FINANCIAL ANALYSIS REQUIREMENTS

### 1. CURRENT MARKET DATA
- Current stock price, recent performance trends, and historical comparison
- Support and resistance prices and how current indicators may drive direction
- 20-day SMA, 50-day SMA, 200-day SMA and directional implications

### 2. FINANCIAL HEALTH METRICS
- Key financial ratios: P/E ratio, forward P/E, Price/Free cash flow, EPS growth, ROE, ROI
- Liquidity ratios: Current ratio, quick ratio
- Leverage ratios: Debt-to-equity, interest coverage
- Profitability: Net profit margin, gross margin, operating margin
- What these ratios indicate about financial health (score 0-100)

### 3. EARNINGS & REVENUE ANALYSIS
- Recent earnings reports and revenue growth/decline trends
- Latest EPS vs estimates (beat/miss/meet)
- Forward earnings guidance and reliability
- Quarterly trends over past 4 quarters

### 4. INDUSTRY & COMPETITIVE POSITION
- Industry comparison vs peers
- Market share and competitive advantages
- Industry growth trends and company positioning

### 5. ANALYST & MARKET SENTIMENT
- Current analyst ratings and target price forecasts
- Recent upgrades/downgrades and reasoning
- Institutional ownership changes
- Real-time sentiment from news, social media (trusted sources only)
- Sentiment score: -1 (very negative) to +1 (very positive)

### 6. NEWS & CATALYSTS
- Major recent news impacting valuation
- Upcoming catalysts (earnings, product launches, regulatory decisions)
- Risk factors and potential headwinds

### 7. INVESTMENT RECOMMENDATION
Based on comprehensive analysis above, provide:
- Overall recommendation: BUY/HOLD/SELL
- Conviction level: High/Medium/Low (translate to 0-100 score)
- Target price (12-month horizon)
- Key investment thesis (2-3 sentences)
- Top 3 risks and top 3 catalysts

## DATA PROVIDED
{json.dumps(data, indent=2, default=str)[:3000]}

## OUTPUT FORMAT
Return analysis as JSON:
{{
    "recommendation": "BUY/HOLD/SELL",
    "conviction_score": 85,
    "target_price": 150.00,
    "financial_health_score": 78,
    "technical_score": 65,
    "sentiment_score": 0.3,
    "key_ratios": {{
        "pe_ratio": 25.4,
        "forward_pe": 22.1,
        "roe": 15.2,
        "debt_to_equity": 0.45,
        "current_ratio": 1.8
    }},
    "support_level": 140.00,
    "resistance_level": 160.00,
    "sma_analysis": {{
        "sma_20": 145.50,
        "sma_50": 142.30,
        "sma_200": 138.90
    }},
    "earnings_quality": "Strong - beat estimates 3/4 quarters",
    "revenue_trend": "Growing 12% YoY",
    "industry_position": "Market leader with 25% share",
    "analyst_consensus": "Buy (12 analysts), avg target $155",
    "recent_news_impact": "Positive - new product launch",
    "investment_thesis": "Strong fundamentals with growing market share in expanding industry. Recent product innovations driving revenue growth.",
    "key_risks": ["Regulatory changes", "Competition", "Economic slowdown"],
    "key_catalysts": ["Q4 earnings", "Product launch", "Market expansion"],
    "data_sources": ["Financial statements", "Analyst reports", "Recent news"]
}}
"""


async def enhanced_stock_analysis(ticker: str, gemini_ask_func) -> Optional[ComprehensiveAnalysis]:
    """Perform comprehensive LLM-based stock analysis."""
    
    try:
        # Fetch comprehensive data
        stock = yf.Ticker(ticker)
        
        # Get all available data
        info = stock.info or {}
        hist = stock.history(period="1y")
        financials = stock.financials
        earnings = stock.earnings
        calendar = stock.calendar
        
        # Prepare comprehensive data package
        data = {
            "basic_info": {
                "symbol": ticker,
                "company_name": info.get("longName", ""),
                "sector": info.get("sector", ""),
                "industry": info.get("industry", ""),
                "market_cap": info.get("marketCap", 0),
                "current_price": info.get("currentPrice", 0)
            },
            "financial_ratios": {
                "pe_ratio": info.get("trailingPE"),
                "forward_pe": info.get("forwardPE"),
                "price_to_book": info.get("priceToBook"),
                "roe": info.get("returnOnEquity"),
                "debt_to_equity": info.get("debtToEquity"),
                "current_ratio": info.get("currentRatio"),
                "profit_margin": info.get("profitMargins"),
                "revenue_growth": info.get("revenueGrowth")
            },
            "price_data": {
                "current": hist["Close"].iloc[-1] if not hist.empty else 0,
                "sma_20": hist["Close"].rolling(20).mean().iloc[-1] if len(hist) >= 20 else None,
                "sma_50": hist["Close"].rolling(50).mean().iloc[-1] if len(hist) >= 50 else None,
                "sma_200": hist["Close"].rolling(200).mean().iloc[-1] if len(hist) >= 200 else None,
                "52_week_high": info.get("fiftyTwoWeekHigh"),
                "52_week_low": info.get("fiftyTwoWeekLow")
            },
            "earnings_data": {
                "last_eps": info.get("trailingEps"),
                "forward_eps": info.get("forwardEps"),
                "earnings_growth": info.get("earningsGrowth"),
                "revenue_per_share": info.get("revenuePerShare"),
                "next_earnings_date": info.get("earningsDate")
            },
            "analyst_data": {
                "target_price": info.get("targetMeanPrice"),
                "recommendation": info.get("recommendationMean"),
                "number_of_analysts": info.get("numberOfAnalystOpinions")
            }
        }
        
        # Create comprehensive prompt
        prompt = create_comprehensive_prompt(ticker, data)
        
        # Get LLM analysis
        response = gemini_ask_func(prompt)
        
        # Parse JSON response
        try:
            analysis_json = json.loads(response)
        except json.JSONDecodeError:
            # Try to extract JSON from markdown if needed
            import re
            json_match = re.search(r'```json\s*(.*?)\s*```', response, re.DOTALL)
            if json_match:
                analysis_json = json.loads(json_match.group(1))
            else:
                print(f"Failed to parse LLM response for {ticker}")
                return None
        
        # Create structured analysis object
        analysis = ComprehensiveAnalysis(
            ticker=ticker,
            recommendation=analysis_json.get("recommendation", "HOLD"),
            conviction_score=analysis_json.get("conviction_score", 50),
            target_price=analysis_json.get("target_price"),
            current_price=data["price_data"]["current"],
            financial_health_score=analysis_json.get("financial_health_score", 50),
            key_ratios=analysis_json.get("key_ratios", {}),
            technical_score=analysis_json.get("technical_score", 50),
            support_level=analysis_json.get("support_level"),
            resistance_level=analysis_json.get("resistance_level"),
            sma_analysis=analysis_json.get("sma_analysis", {}),
            earnings_quality=analysis_json.get("earnings_quality", "Unknown"),
            revenue_trend=analysis_json.get("revenue_trend", "Unknown"),
            industry_position=analysis_json.get("industry_position", "Unknown"),
            sentiment_score=analysis_json.get("sentiment_score", 0.0),
            analyst_consensus=analysis_json.get("analyst_consensus", "Unknown"),
            recent_news_impact=analysis_json.get("recent_news_impact", "Unknown"),
            investment_thesis=analysis_json.get("investment_thesis", ""),
            key_risks=analysis_json.get("key_risks", []),
            key_catalysts=analysis_json.get("key_catalysts", []),
            analysis_date=datetime.now(),
            data_sources=analysis_json.get("data_sources", [])
        )
        
        return analysis
        
    except Exception as e:
        print(f"Error analyzing {ticker}: {e}")
        return None


async def batch_enhanced_analysis(
    tickers: List[str],
    gemini_ask_func,
    max_concurrent: int = 3,
    show_progress: bool = True
) -> List[ComprehensiveAnalysis]:
    """Analyze multiple stocks with comprehensive LLM analysis and rate limiting."""

    if not tickers:
        return []

    if show_progress:
        print(f"   🧠 Preparing comprehensive analysis for {len(tickers)} stocks...")

    # Prepare all data first (this is fast and doesn't use LLM calls)
    stock_data = {}
    failed_data_fetch = []

    for ticker in tickers:
        try:
            # Fetch comprehensive data (no LLM calls here)
            stock = yf.Ticker(ticker)
            info = stock.info or {}
            hist = stock.history(period="1y")

            if hist.empty:
                failed_data_fetch.append(ticker)
                continue

            # Prepare data package
            data = {
                "basic_info": {
                    "symbol": ticker,
                    "company_name": info.get("longName", ""),
                    "sector": info.get("sector", ""),
                    "industry": info.get("industry", ""),
                    "market_cap": info.get("marketCap", 0),
                    "current_price": info.get("currentPrice", 0)
                },
                "financial_ratios": {
                    "pe_ratio": info.get("trailingPE"),
                    "forward_pe": info.get("forwardPE"),
                    "price_to_book": info.get("priceToBook"),
                    "roe": info.get("returnOnEquity"),
                    "debt_to_equity": info.get("debtToEquity"),
                    "current_ratio": info.get("currentRatio"),
                    "profit_margin": info.get("profitMargins"),
                    "revenue_growth": info.get("revenueGrowth")
                },
                "price_data": {
                    "current": hist["Close"].iloc[-1],
                    "sma_20": hist["Close"].rolling(20).mean().iloc[-1] if len(hist) >= 20 else None,
                    "sma_50": hist["Close"].rolling(50).mean().iloc[-1] if len(hist) >= 50 else None,
                    "sma_200": hist["Close"].rolling(200).mean().iloc[-1] if len(hist) >= 200 else None,
                    "52_week_high": info.get("fiftyTwoWeekHigh"),
                    "52_week_low": info.get("fiftyTwoWeekLow")
                },
                "earnings_data": {
                    "last_eps": info.get("trailingEps"),
                    "forward_eps": info.get("forwardEps"),
                    "earnings_growth": info.get("earningsGrowth"),
                    "revenue_per_share": info.get("revenuePerShare"),
                    "next_earnings_date": info.get("earningsDate")
                },
                "analyst_data": {
                    "target_price": info.get("targetMeanPrice"),
                    "recommendation": info.get("recommendationMean"),
                    "number_of_analysts": info.get("numberOfAnalystOpinions")
                }
            }

            stock_data[ticker] = data

        except Exception as e:
            print(f"     ⚠️  Failed to fetch data for {ticker}: {e}")
            failed_data_fetch.append(ticker)

    if failed_data_fetch:
        print(f"   ⚠️  Skipped {len(failed_data_fetch)} stocks due to data fetch failures")

    valid_tickers = list(stock_data.keys())
    if not valid_tickers:
        print("   ❌ No stocks have valid data for analysis")
        return []

    # Create prompts for all valid stocks
    prompts = [create_comprehensive_prompt(ticker, stock_data[ticker]) for ticker in valid_tickers]

    # Use enhanced batch function with rate limiting
    from llm import ask_gemini_enhanced_batch

    if show_progress:
        print(f"   🚀 Sending {len(valid_tickers)} stocks to LLM for analysis...")

    llm_results = await ask_gemini_enhanced_batch(
        valid_tickers,
        prompts,
        gemini_ask_func,
        max_concurrent=max_concurrent,
        show_progress=show_progress
    )

    # Parse results into ComprehensiveAnalysis objects
    analyses = []
    for ticker, response in llm_results:
        try:
            analysis = parse_llm_response(ticker, response, stock_data[ticker])
            if analysis:
                analyses.append(analysis)
        except Exception as e:
            print(f"     ❌ Failed to parse analysis for {ticker}: {e}")

    # Sort by conviction score (highest first)
    analyses.sort(key=lambda x: x.conviction_score, reverse=True)

    if show_progress:
        print(f"   ✅ Successfully analyzed {len(analyses)}/{len(tickers)} stocks")

    return analyses


def parse_llm_response(ticker: str, response: str, data: Dict[str, Any]) -> Optional[ComprehensiveAnalysis]:
    """Parse LLM response into ComprehensiveAnalysis object."""
    try:
        # Try to extract JSON from response
        import re
        import json

        # Handle markdown-wrapped JSON
        json_match = re.search(r'```json\s*(.*?)\s*```', response, re.DOTALL)
        if json_match:
            analysis_json = json.loads(json_match.group(1))
        else:
            # Try direct JSON parsing
            analysis_json = json.loads(response)

        # Create analysis object
        analysis = ComprehensiveAnalysis(
            ticker=ticker,
            recommendation=analysis_json.get("recommendation", "HOLD"),
            conviction_score=analysis_json.get("conviction_score", 50),
            target_price=analysis_json.get("target_price"),
            current_price=data["price_data"]["current"],
            financial_health_score=analysis_json.get("financial_health_score", 50),
            key_ratios=analysis_json.get("key_ratios", {}),
            technical_score=analysis_json.get("technical_score", 50),
            support_level=analysis_json.get("support_level"),
            resistance_level=analysis_json.get("resistance_level"),
            sma_analysis=analysis_json.get("sma_analysis", {}),
            earnings_quality=analysis_json.get("earnings_quality", "Unknown"),
            revenue_trend=analysis_json.get("revenue_trend", "Unknown"),
            industry_position=analysis_json.get("industry_position", "Unknown"),
            sentiment_score=analysis_json.get("sentiment_score", 0.0),
            analyst_consensus=analysis_json.get("analyst_consensus", "Unknown"),
            recent_news_impact=analysis_json.get("recent_news_impact", "Unknown"),
            investment_thesis=analysis_json.get("investment_thesis", ""),
            key_risks=analysis_json.get("key_risks", []),
            key_catalysts=analysis_json.get("key_catalysts", []),
            analysis_date=datetime.now(),
            data_sources=analysis_json.get("data_sources", [])
        )

        return analysis

    except Exception as e:
        print(f"     ❌ Failed to parse LLM response for {ticker}: {e}")
        return None
