#!/usr/bin/env python3
"""
Stock Analyst Pro – Gemini‑Only Edition (v 1.2)
============================================
A fully self‑contained command‑line agent built with **Agno** that analyses
stocks, remembers user context, ingests documents, and produces actionable
investment plans.  
**New in this release**: *adaptive* DCA/stop logic that chooses sensible price
levels when the user does not specify them.
"""
from __future__ import annotations

import argparse
import json
import logging
import os
import sys
from dataclasses import dataclass
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

import pandas as pd
import yfinance as yf

from dotenv import load_dotenv

load_dotenv(override=True)


# ---------------------------------------------------------------------------
# Optional heavy dependencies (all degrade gracefully when missing)
# ---------------------------------------------------------------------------
try:
    from agno.agent import Agent
    from agno.memory import FileMemory, QdrantMemory
    from agno.workflow import TaskGraph, task
except ImportError:  # pragma: no cover
    Agent = None  # type: ignore
    TaskGraph = None  # type: ignore

try:
    from llama_index import VectorStoreIndex, StorageContext
    from llama_index.readers import SimpleDirectoryReader
    from llama_index.vector_stores.qdrant import QdrantVectorStore
except ImportError:  # pragma: no cover
    VectorStoreIndex = None  # type: ignore

try:
    from vaderSentiment.vaderSentiment import SentimentIntensityAnalyzer
    _VADER = SentimentIntensityAnalyzer()
except ImportError:  # pragma: no cover
    _VADER = None

try:
    import google.generativeai as genai
except ImportError:  # pragma: no cover
    genai = None

# ---------------------------------------------------------------------------
# Configuration & logging
# ---------------------------------------------------------------------------
PROFILE_FILE = Path.home() / ".stock_agent_profile.json"
MEM_PATH     = Path.home() / ".stock_agent_memory.json"
KB_PATH      = Path.home() / "stock_agent_kb"

DEFAULT_TICKERS = [
    "AAPL", "MSFT", "AMZN", "GOOGL", "META", "TSLA", "NVDA",
]

logging.basicConfig(level=logging.INFO, format="%(levelname)s: %(message)s")
log = logging.getLogger("stock_agent")

# ---------------------------------------------------------------------------
# Gemini helper & Agno‑compatible chat adapter
# ---------------------------------------------------------------------------

def _init_gemini() -> None:
    if genai is None:
        raise RuntimeError("google‑generativeai not installed – `pip install google-generativeai`.  ")
    key = os.environ.get("GOOGLE_API_KEY")
    if not key:
        raise RuntimeError("Set GEMINI_API_KEY environment variable.")
    genai.configure(api_key=key, transport="rest")


def gemini_ask(prompt: str, *, system: str | None = None) -> str:
    _init_gemini()
    model_name = os.environ.get("GEMINI_MODEL", "models/gemini-2.0-flash")
    model = genai.GenerativeModel(model_name)
    full_prompt = f"{system}\n{prompt}" if system else prompt
    try:
        return model.generate_content(full_prompt).text.strip()
    except Exception as e:
        print("Gemini error:", e)
        return "[Gemini quota error]"


class GeminiChat:
    """Minimal adapter so Agno can call Gemini like an OpenAI‑style chat model."""

    def __init__(self, *, temperature: float = 0.2):
        _init_gemini()
        model_name = os.environ.get("GEMINI_MODEL", "models/gemini-2.0-flash")
        self._model = genai.GenerativeModel(model_name)
        self.temperature = temperature

    def chat(self, messages: List[Dict[str, str]]) -> str:  # noqa: D401
        convo = "\n".join(f"{m['role'].capitalize()}: {m['content']}" for m in messages)
        reply = self._model.generate_content(
            convo,
            generation_config={"temperature": self.temperature},
        )
        return reply.text.strip()

# ---------------------------------------------------------------------------
# Risk‑profile dataclass & interactive quiz
# ---------------------------------------------------------------------------
@dataclass
class RiskProfile:
    level: str  # "Low" | "Medium" | "High"

    def save(self) -> None:
        PROFILE_FILE.write_text(json.dumps({"risk": self.level}, indent=2))
        log.info("Saved risk profile: %s", self.level)

    @classmethod
    def load(cls) -> "RiskProfile":
        if PROFILE_FILE.exists():
            lvl = json.loads(PROFILE_FILE.read_text()).get("risk", "Medium")
            return cls(lvl)
        return cls("Medium")


def risk_quiz() -> str:
    score = 0
    horizon = input("Investment horizon (Short / Medium / Long): ").lower()
    score += 0 if horizon.startswith("s") else 1 if horizon.startswith("m") else 2

    drawdown = input("If your portfolio fell 20 % tomorrow, you would (Sell / Hold / Buy more): ").lower()
    score += 0 if drawdown.startswith("s") else 1 if drawdown.startswith("h") else 2

    style = input("Preference (Steady income / Balanced / Maximum growth): ").lower()
    score += 0 if style.startswith("steady") else 1 if style.startswith("balanced") else 2

    return "Low" if score <= 2 else "Medium" if score <= 4 else "High"

# ---------------------------------------------------------------------------
# Market‑data helpers
# ---------------------------------------------------------------------------

def fetch_quote(ticker: str) -> yf.Ticker:
    return yf.Ticker(ticker)


def moving_avgs(series: pd.Series) -> Dict[str, float]:
    return {f"ma{w}": series.rolling(w).mean().iloc[-1] for w in (20, 50, 200)}


def technical_snapshot(ticker: str) -> Dict[str, Any]:
    hist = fetch_quote(ticker).history("2y")
    if hist.empty:
        raise ValueError(f"No price data for {ticker}")
    close = hist["Close"]
    return {
        "current_price": close.iloc[-1],
        **moving_avgs(close),
    }


def fundamental_snapshot(ticker: str) -> Dict[str, Any]:
    info = fetch_quote(ticker).info or {}
    return {
        "pe": info.get("trailingPE"),
        "pb": info.get("priceToBook"),
        "div_yield": info.get("dividendYield"),
    }


def earnings_snapshot(ticker: str) -> Dict[str, Any]:
    q = fetch_quote(ticker)
    cal = q.calendar.T.to_dict() if not q.calendar.empty else {}
    eps = q.earnings.iloc[-1].to_dict() if not q.earnings.empty else {}
    return {"calendar": cal, "last_annual_earnings": eps}


def news_and_sentiment(ticker: str, max_items: int = 5) -> Dict[str, Any]:
    raw = (fetch_quote(ticker).news or [])[:max_items]
    heads = [n["title"] for n in raw]
    if _VADER and heads:
        scores = [_VADER.polarity_scores(h)["compound"] for h in heads]
        avg = sum(scores) / len(scores)
    else:
        avg = 0.0
    return {"headlines": heads, "avg_sentiment": avg}

# ---------------------------------------------------------------------------
# Screening logic
# ---------------------------------------------------------------------------

def screen_stocks(risk: str, tickers: Optional[List[str]] = None) -> List[Tuple[str, float]]:
    tickers = tickers or DEFAULT_TICKERS
    ranking: List[Tuple[str, float]] = []
    for t in tickers:
        try:
            tech, funda = technical_snapshot(t), fundamental_snapshot(t)
        except Exception as err:  # pragma: no cover – network issues
            log.debug("Skipping %s: %s", t, err)
            continue
        score = 0.0
        score += tech["current_price"] > tech["ma200"]
        score += funda["pe"] and funda["pe"] < 30
        if risk == "Low" and funda["div_yield"]:
            score += 0.5
        ranking.append((t, score))
    return sorted(ranking, key=lambda x: x[1], reverse=True)[:5]

# ---------------------------------------------------------------------------
# Adaptive investment‑plan engine
# ---------------------------------------------------------------------------
@dataclass
class Plan:
    now_qty: int
    dca_price: float
    dca_qty: int
    stop_price: float

    def pretty(self, ticker: str, price: float) -> str:
        dip_pct  = (price - self.dca_price)  / price * 100
        stop_pct = (price - self.stop_price) / price * 100
        return (
            f"Buy {self.now_qty} shares of {ticker} now.\n"
            f"If price dips to €{self.dca_price:.2f} (≈-{dip_pct:.1f}%), buy {self.dca_qty} more.\n"
            f"Set stop‑loss at €{self.stop_price:.2f} (≈-{stop_pct:.1f}%)."
        )


def _auto_levels(price: float, ma50: float | None) -> Tuple[float, float]:
    """Choose DCA & stop if user didn’t supply them."""
    if ma50 and 0.03 < (price - ma50) / price < 0.12:  # 3–12 % below market
        dca = ma50
    else:
        dca = price * 0.90  # 10 % dip
    stop = dca * 0.95       # 5 % below DCA
    return dca, stop


def draft_plan(
    ticker: str,
    cash: float,
    *,
    upfront_fraction: float = 0.6,
    dip_pct: float | None = None,
    stop_pct: float | None = None,
) -> Plan:
    tech = technical_snapshot(ticker)
    price_now = tech["current_price"]

    if dip_pct is None or stop_pct is None:
        dca, stop = _auto_levels(price_now, tech.get("ma50"))
    else:
        dca   = price_now * (1 - dip_pct)
        stop  = price_now * (1 - stop_pct)

    now_qty = max(int((cash * upfront_fraction) // price_now), 1)
    dca_qty = max(int(((cash * (1 - upfront_fraction)) // dca)), 1)
    return Plan(now_qty, dca, dca_qty, stop)

# ---------------------------------------------------------------------------
# Knowledge‑base wrapper (LlamaIndex + Qdrant)
# ---------------------------------------------------------------------------
class KnowledgeBase:
    def __init__(self):
        self.enabled = VectorStoreIndex is not None
        if not self.enabled:
            return
        if os.getenv("QDRANT_URL"):
            store = QdrantVectorStore("stocks_kb", url=os.getenv("QDRANT_URL"))
            ctx   = StorageContext.from_defaults(vector_store=store)
            self.index = VectorStoreIndex([], storage_context=ctx)
        else:
            KB_PATH.mkdir(exist_ok=True)
            self.index = VectorStoreIndex.from_documents([], persist_dir=str(KB_PATH))

    def ingest(self, path: str) -> None:
        if not self.enabled:
            print("[Knowledge base disabled]")
            return
        docs = SimpleDirectoryReader(input_files=[path]).load_data()
        self.index.insert_documents(docs)
        self.index.storage_context.persist()
        print(f"Ingested {len(docs)} document(s)")

    def query(self, q: str) -> str:
        if not self.enabled:
            return "[KB disabled]"
        return str(self.index.query(q))

KB = KnowledgeBase()

# ---------------------------------------------------------------------------
# Agno agent & deterministic workflow
# ---------------------------------------------------------------------------

def build_agent() -> Optional[Agent]:
    if Agent is None:
        return None
    model   = GeminiChat(temperature=0.2)
    memory  = QdrantMemory("stock_mem") if os.getenv("QDRANT_URL") else FileMemory(MEM_PATH)
    agent   = Agent("StockAnalystPro", model, memory, markdown=True)

    # Register remaining tools
    agent.tool(screen_stocks)
    agent.tool(technical_snapshot)
    agent.tool(fundamental_snapshot)
    agent.tool(earnings_snapshot)
    agent.tool(news_and_sentiment)
    agent.tool(draft_plan, name="investment_plan")
    agent.tool(KB.query, name="knowledge_query")
    agent.tool(gemini_ask, name="ask_gemini")
    return agent


# ---------------------------------------------------------------------------
# Deterministic workflow (Agno TaskGraph)
# ---------------------------------------------------------------------------
if TaskGraph:

    class AnalyseTickerWorkflow(TaskGraph):
        """Fetch data → Gemini report → return BUY/HOLD/SELL."""

        ticker: str

        @task
        def gather(self):
            return {
                **technical_snapshot(self.ticker),
                **fundamental_snapshot(self.ticker),
                "earnings": earnings_snapshot(self.ticker),
                "news": news_and_sentiment(self.ticker),
            }

        @task
        def llm(self, data):
            prompt = (
                f"You are a CFA. Analyse {self.ticker} using the JSON below "
                f"and write a concise report (≈180 words). Finish with BUY, "
                f"HOLD or SELL on its own line.\n\n{json.dumps(data)}"
            )
            return gemini_ask(prompt)

        @task
        def output(self, report):
            return report
else:
    AnalyseTickerWorkflow = None  # type: ignore


# ---------------------------------------------------------------------------
# CLI sub‑command handlers
# ---------------------------------------------------------------------------
def cmd_setup(_: argparse.Namespace) -> None:
    level = risk_quiz()
    RiskProfile(level).save()


def cmd_discover(_: argparse.Namespace) -> None:
    prof = RiskProfile.load()
    picks = screen_stocks(prof.level)
    print(f"Top ideas for {prof.level} risk:")
    for t, s in picks:
        print(f"  {t}: score {s:.2f}")

    if (ag := build_agent()):
        ag.remember("discover", picks)

    print("\nGemini says:\n", gemini_ask(f"Explain why {picks} suit a {prof.level} risk investor."))


def cmd_analyse(args: argparse.Namespace) -> None:
    tic = args.ticker.upper()
    if AnalyseTickerWorkflow:
        print(AnalyseTickerWorkflow(ticker=tic).run())
    else:
        data = {
            **technical_snapshot(tic),
            **fundamental_snapshot(tic),
            "earnings": earnings_snapshot(tic),
            "news": news_and_sentiment(tic),
        }
        print(json.dumps(data, indent=2))


def cmd_plan(args: argparse.Namespace) -> None:
    plan = draft_plan(args.ticker.upper(), args.cash)
    price_now = technical_snapshot(args.ticker.upper())["current_price"]
    print(plan.pretty(args.ticker.upper(), price_now))

    if (ag := build_agent()):
        ag.remember(f"plan_{args.ticker.upper()}_{args.cash}", plan.__dict__)


def cmd_ingest(args: argparse.Namespace) -> None:
    KB.ingest(args.path)


def cmd_ask(args: argparse.Namespace) -> None:
    print(KB.query(args.question))


def cmd_chat(_: argparse.Namespace) -> None:
    ag = build_agent()
    if not ag:
        print("Chat mode requires the Agno package.")
        return

    print("Interactive chat – type 'exit' to quit.")
    while True:
        try:
            msg = input("You: ")
        except (EOFError, KeyboardInterrupt):
            print()
            break
        if msg.lower() in {"exit", "quit"}:
            break
        print("Agent:", ag.run(msg))


# ---------------------------------------------------------------------------
# Argument parser & entrypoint
# ---------------------------------------------------------------------------
def build_parser() -> argparse.ArgumentParser:
    p = argparse.ArgumentParser(description="Stock Analyst Pro CLI")
    sub = p.add_subparsers(dest="cmd", required=True)

    sub.add_parser("setup", help="Run risk‑tolerance quiz")
    sub.add_parser("discover", help="Screen market for ideas")

    ana = sub.add_parser("analyse", help="Deep‑dive on a ticker")
    ana.add_argument("ticker")

    pl = sub.add_parser("plan", help="Generate investment plan")
    pl.add_argument("ticker")
    pl.add_argument("cash", type=float)

    ing = sub.add_parser("ingest", help="Add PDF/HTML/CSV to knowledge base")
    ing.add_argument("path")

    ask = sub.add_parser("ask", help="Query the document knowledge base")
    ask.add_argument("question")

    sub.add_parser("chat", help="Interactive chat agent")

    return p


def main() -> None:
    args = build_parser().parse_args()
    {
        "setup":    cmd_setup,
        "discover": cmd_discover,
        "analyse":  cmd_analyse,
        "plan":     cmd_plan,
        "ingest":   cmd_ingest,
        "ask":      cmd_ask,
        "chat":     cmd_chat,
    }[args.cmd](args)


if __name__ == "__main__":
    main()
